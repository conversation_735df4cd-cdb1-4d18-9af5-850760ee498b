<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_RiskAnalysis
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        if (isset($data['Name Gruppenleiter'])) {
            $data['Name Gruppenleiter'] = PrintoutHelper::downloadEmployee(
                $data['Name Gruppenleiter'], $curl)['displayName'];
        }
        return $data;
    }
}
