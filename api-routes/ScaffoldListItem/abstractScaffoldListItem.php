<?php
require_once __DIR__ . '/../../printout.helper.php';

abstract class AbstractScaffoldListItem
{
    /**
     * @var array key is the URL and value is the API response
     */
    private array $downloadedDirectoryofservicesItems = [];

    public function getData($ktr, $intnr): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $data['scaffoldListItem'] = $this->downloadScaffoldListItem($ktr, $intnr, $curl);
        $data['scaffoldListItem']['orderNumberProcessed'] = $this->prepareOrderNumber($data['scaffoldListItem']);
        $measurementsListsArray = $this->downloadScaffoldListMeasurement($ktr, $intnr, $curl);
        $data['scaffoldListMeasurement'] = $this->processMeasurementLists($measurementsListsArray, $curl, $data);
        $data['projectData'] = PrintoutHelper::downloadProject($ktr, 'customerName', $curl);
        $data['location'] = $this->downloadLocation($ktr, $intnr, $curl);
        $createdBy = $data['scaffoldListItem']['createdBy'] ?? null;
        $data['employee'] = $createdBy ? $this->downloadEmployee($createdBy, $curl) : '';
        if (!empty($data['scaffoldListItem']['payeeVirtualNo'])) {
            $partner = $this->downloadPartner($data['scaffoldListItem']['payeeVirtualNo'], $curl);
            $name = empty($partner) ? '' : "{$partner[0]['firstName']} {$partner[0]['lastName']}";
            $data['partnerName'] = $name;
        }
        return $data;
    }

    protected function prepareOrderNumber(array $scaffoldListItem): string
    {
        $orderNumber = trim($scaffoldListItem['orderNumber'] ?? '');
        if ($orderNumber == "") {
            return "";
        }
        if (preg_match('/WO(\d+)$/', $orderNumber, $matches)) {
            return $matches[1];
        }
        return "";
    }

    protected function processMeasurementLists(array $measurementListsArray, $curl, array $data): array
    {
        return $this->prepareMeasurementListsByDownloading($measurementListsArray, $curl, $data);
    }

    protected function downloadScaffoldListItem($ktr, $intnr, $curl)
    {
        $response = json_decode($curl->_simple_call('get',
            "v1/scaffoldlists/select/$ktr/$intnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        if ($response) {
            return $response[0];
        }
        return [];
    }

    protected function downloadScaffoldListMeasurement($ktr, $intnr, PrintoutCurl $curl)
    {
        $url = "v1/scaffoldlists/measurements/$ktr-$intnr";
        $response = json_decode(
            $curl->_simple_call('GET', $url, [], PrintoutHelper::getHeadersForApiCalls()), true
        );

        return $response ?? [];
    }

    protected function prepareMeasurementListsByDownloading($measurementListsArray, $curl, $data): array
    {
        $result = [];
        $montTyp = $data['scaffoldListItem']['monttyp'] ?? null;
        foreach ($measurementListsArray as $measurement) {
            $measurement['shortDescription'] = $this->downloadMeasurementListDescription($measurement, $curl, $montTyp);
            $result[] = $measurement;
        }
        return $result;
    }

    protected function downloadMeasurementListDescription($measurement, $curl, $monttyp): string
    {
        static $translations = null;
        if ($translations === null) {
            $translationFile = $this->getTranslationFilePath();
            $translations = json_decode(file_get_contents($translationFile), true);
        }

        $dos = $measurement['directoryOfServicesId'];
        $serviceItemNo = $measurement['serviceItemNo'];
        $response = [];

        if ($serviceItemNo != '') {
            $url = "v1/directoryofservices/items?compoundKey=$dos-$serviceItemNo";
            if (array_key_exists($url, $this->downloadedDirectoryofservicesItems)) {
                $response = $this->downloadedDirectoryofservicesItems[$url];
            } else {
                $response = json_decode($curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls()), true);
                $this->downloadedDirectoryofservicesItems[$url] = $response;
            }
        }

        $fields = [
            8 => ['mountingText', 'basicRentalText'],
            16 => ['basicRentalText'],
            24 => ['mountingText', 'basicRentalText', 'extraRentalText'],
            56 => ['mountingText', 'basicRentalText', 'extraRentalText', 'dismantlingText'],
            40 => ['mountingText', 'basicRentalText', 'dismantlingText']
        ];

        foreach ($translations as $translation) {
            if ($translation['directoryOfServiceId'] == $dos && $translation['serviceItemNo'] == $serviceItemNo) {
                $keys = $fields[(int)$monttyp] ?? ['mountingText', 'dismantlingText', 'extraRentalText'];
                return implode(', ', array_filter(
                    array_map(function ($key) use ($translation) {
                        if ($key === 'basicRentalText') {
                            return empty($translation['basicRentalText'])
                                ? ($translation['extraRentalText'] ?? null)
                                : $translation['basicRentalText'];
                        }
                        return $translation[$key] ?? null;
                    }, $keys)
                ));
            }
        }
        return $response[0]['shortDescription'] ?? '';
    }

    protected function downloadLocation($ktr, $id, $curl): string
    {
        $response = json_decode($curl->_simple_call('get',
            "v2/scaffoldlists/$ktr/$id",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        return $response ? $response[0]['location'] : '';
    }

    protected function downloadEmployee($createdBy, $curl): array
    {
        $response = json_decode($curl->_simple_call('get',
            "v3/employees/$createdBy",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        return $response ?: [];
    }

    protected function downloadPartner(int $virtualNo, $curl): array
    {
        $url = "v1/partners?virtualNo=$virtualNo";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }

    abstract protected function getTranslationFilePath(): string;
}
