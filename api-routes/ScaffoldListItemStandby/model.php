<?php
require_once __DIR__ . '/../ScaffoldListItem/abstractScaffoldListItem.php';

class C_ScaffoldListItemStandby extends AbstractScaffoldListItem
{
    /**
     * Return the downloaded measurement array without additional processing;
     *  - different from ScaffoldListItemRental and ScaffoldListItem.
     */
    protected function processMeasurementLists(array $measurementListsArray, $curl, array $data): array
    {
        return $measurementListsArray;
    }

    /**
     * Since measurement description processing and translation is not needed in Standby,
     * we can simply return an empty string.
     */
    protected function getTranslationFilePath(): string
    {
        return '';
    }
}
