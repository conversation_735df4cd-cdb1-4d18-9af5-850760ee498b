<?php
require_once __DIR__ . "/../../printout.helper.php";
require_once __DIR__ . "/model.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (empty($data)) {
    $data = (new C_ScaffoldListItemStandby())->getData($_GET['ktr'], $_GET['intnr']);
}

function getMeasurementLengthOrCount(array $measurement): float
{
    if (($measurement['quantityLength'] ?? '') != "") {
        return (float)$measurement['quantityLength'];
    }
    if (($measurement['quantityCount'] ?? '') != "") {
        return (float)$measurement['quantityCount'];
    }
    return 0;
}

function renderMeasurementCell(?array $measurement, int $index): string
{
    if (!$measurement) {
        return "<td></td><td></td><td></td>";
    }
    $decimalHours = getMeasurementLengthOrCount($measurement);
    $hours = floor($decimalHours);
    $minutes = round(($decimalHours - $hours) * 60);
    $formattedHours = sprintf("%d:%02dч.", $hours, $minutes);
    $detailDescription = $measurement['detailDescription'] ?? "";

    return "
        <td class='italic'>$index</td>
        <td class='italic' style='text-align: left !important'>
            <div>$detailDescription</div>
        </td>
        <td class='italic p-4'>$formattedHours</td>
    ";
}

$measurements = $data['scaffoldListMeasurement'] ?? [];
$sumDecimalHours = 0.0;
foreach ($measurements as $measurement) {
    $sumDecimalHours += getMeasurementLengthOrCount($measurement);
}

$unitPrice = 0.0;
if (!empty($measurements)) {
    $firstMeasurement = reset($measurements);
    $unitPrice = (float)($firstMeasurement['unitPrice'] ?? 0);
}

$totalCost = $sumDecimalHours * $unitPrice;
$sumHoursInt = floor($sumDecimalHours);
$sumHoursMin = round(($sumDecimalHours - $sumHoursInt) * 60);
$sumHoursFormat = sprintf("%d:%02dч.", $sumHoursInt, $sumHoursMin);

$formattedCost = number_format($totalCost, 2, ',', '');
$leftMeasurements = array_slice($measurements, 0, 15);
$rightMeasurements = array_slice($measurements, 15);
$dataRows = max(count($leftMeasurements), count($rightMeasurements));

$blankRow = "<tr class='table-row' style='height:27px;'>
    <td></td><td></td><td></td>
    </tr>";

$totalRowsInTable = 15;
$extraBlankRows = $totalRowsInTable - ($dataRows + 1);
$extraBlankRows = max(0, $extraBlankRows);
$hasRightData = (count($rightMeasurements) > 0);

$totalHoursSVG = '
    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="100" viewBox="0 0 80 150">
        <g transform="rotate(-90, 40, 75)">
            <text x="50" y="70" text-anchor="middle" dominant-baseline="middle" style="font-size: 30px;">
                <tspan x="40" dy="0">Total hours /</tspan>
                <tspan x="40" dy="35">Общо часове</tspan>
            </text>
        </g>
    </svg>
';
?>

<!doctype html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/style.css') ?>">
    <title>Activity SHEET</title>
</head>
<body>
<h3 class="text-align-center">ACTIVITY SHEET / ПРИСЪСТВЕНА ФОРМА</h3>
<?php require_once __DIR__ . "/../ScaffoldListItem/header_table.php"; ?>
<table class="rental-table-first font-bold">
    <tr>
        <td colspan="<?= $hasRightData ? '6' : '3' ?>" style="text-align: center">
            Date/Дата&nbsp;&nbsp;&nbsp;
            <?= empty($data['scaffoldListItem']['vhbeg'])
                ? '' : date("d.m.Y", strtotime($data['scaffoldListItem']['vhbeg'])) . 'г.' ?>
        </td>
    </tr>
    <tr>
        <td></td>
        <td>Name/Име</td>
        <td class="total-hours-vertical"><?= $totalHoursSVG ?></td>
        <?php if ($hasRightData) { ?>
            <td></td>
            <td>Name/Име</td>
            <td class="total-hours-vertical"><?= $totalHoursSVG ?></td>
        <?php } ?>
    </tr>

    <?php
    for ($i = 0; $i < $dataRows; $i++) {
        $leftMeasurement = $leftMeasurements[$i] ?? null;
        $rightMeasurement = $rightMeasurements[$i] ?? null;

        $leftIndex = $i + 1;
        $rightIndex = 15 + ($i + 1);

        echo "<tr>";
        echo renderMeasurementCell($leftMeasurement, $leftIndex);
        if ($hasRightData) {
            echo renderMeasurementCell($rightMeasurement, $rightIndex);
        }
        echo "</tr>";
    }
    ?>
    <tr>
        <?php if ($hasRightData) { ?>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="italic p-4"><?= $sumHoursFormat ?></td>
        <?php } else { ?>
            <td></td>
            <td></td>
            <td class="italic p-4"><?= $sumHoursFormat ?></td>
        <?php } ?>
    </tr>

    <?php
    for ($i = 0; $i < $extraBlankRows; $i++) {
        echo $blankRow;
    }
    ?>
</table>

<table class="remarks-table font-bold">
    <tr>
        <td colspan="3" class="header">REMARKS / USED MATERIALS<br>ЗАБЕЛЕЖКИ / ИЗПОЛЗВАНИ МАТЕРИАЛИ</td>
    </tr>
    <tr>
        <td colspan="3" style="border: none">
            <div class="calculation">
                <?php
                $formattedUnitPrice = number_format($unitPrice, 2, ',', '');
                echo "$sumHoursFormat x $formattedUnitPrice = $formattedCost лв.";
                ?>
            </div>
        </td>
    </tr>
    <tr>
        <td>DATE / ДАТА<br><br>
            <span>
                <?= empty($data['scaffoldListItem']['datum']) ?
                    '' : (date("d.m.Y", strtotime($data['scaffoldListItem']['datum'])) . 'г.')
                ?>
            </span>
        </td>
        <td>
            Name + signature Company<br>Име + подпис за фирмата<br><br>
            <span class="signature"><?= $data['employee']['displayName'] ?? '' ?></span>
        </td>
        <td>
            Name + signature Aurubis<br>Име + подпис за Аурубис<br><br>
            <span class="signature"><?= $data['partnerName'] ?? '' ?></span>
        </td>
    </tr>
</table>

<div class="footer">
    <span>Remark: Obligation of the company is to fill in and present this form</span><br>
    <span>Забележка: Задължение на фирмата е да попълва и представя тази форма</span>
</div>

</body>
</html>
