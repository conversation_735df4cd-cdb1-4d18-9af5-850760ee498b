<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_ScaffoldingInvoice
{
    public function getData($year, $invoiceNo): array
    {
        $curl = new PrintoutCurl();
        return $this->getInvoice($curl, $year, $invoiceNo);
    }

    public function getInvoice($curl, $year, $invoiceNo)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/receipts/invoice/$year/$invoiceNo",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json, true);
    }
}
