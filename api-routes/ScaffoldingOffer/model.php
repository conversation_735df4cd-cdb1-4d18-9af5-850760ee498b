<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_ScaffoldingOffer
{
    public function getData($quotationNumber): array
    {
        $curl = new PrintoutCurl();
        $data = [];

        $quotation = $this->getQuotation($curl, $quotationNumber)[0];
        $data['quotation'] = $quotation;
        $data['quotationItems'] = $this->getQuotationItems($curl, $quotationNumber);

        $data['orderDate'] = isset($quotation['orderDate']) ? date_format(date_create($data['quotation']['orderDate']), 'd.m.Y') : '';
        $data['quotationCity'] = $quotation["addressCity"] ?? '';
        return $data;
    }

    public function getQuotation($curl, $quotationNr)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get', "$base/v1/quotation/$quotationNr/0",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public function getQuotationItems($curl, $quotationNr)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get', "$base/v1/quotation/$quotationNr/0/items",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}