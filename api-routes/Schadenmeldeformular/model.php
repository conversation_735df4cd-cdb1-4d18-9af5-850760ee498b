<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Schadenmeldeformular
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl();

        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $mapped = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        $displayInsideChildren = $this->getDisplayInside($doc['fullDocument']);

        $templateData['mapped'] = array_merge($mapped, $displayInsideChildren);

        $templateData['Name Gruppenleiter'] = PrintoutHelper::downloadEmployee(
            $mapped['Schadenverursacher Name Gruppenleiter'], $curl)['displayName'];

        return $templateData;
    }

    public function getDisplayInside($document): array
    {
        if (!isset($document['children']))
            return [];
        $data = [];
        foreach ($document['children'] as $parent) {
            foreach ($document['children'] as $child) {
                if (isset($child['displayInside']) && $parent['id'] === $child['displayInside']) {
                    $title = $parent['title'] . ' ' . $child['title'];
                    $data[$title] = $child['reportedValue'];
                }
            }
        }

        return $data;
    }
}
