<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Stundenlohnarbeiten())->getData($_GET['schemaId'], $_GET['documentId']);
}

function getDocumentData($title, $data, $prepend)
{
    if (isset($data['documentData'][$title]))
        echo sprintf("<h5>%s </h5><p>%s</p>", $prepend, $data['documentData'][$title][0]);
}

function printInnerData($title, $unit, $data, $prepend = "")
{
    $key = 'Eingesetztes Werkzeug / Bauschutt/ Sonstiges';

    if (isset($data['documentData'][$key][$title])) {
        $prefix = $prepend === "" ? "" : ($prepend . " ");
        echo sprintf("<tr><td>%s%s</td></tr>", $prefix, $data['documentData'][$key][$title][0] . ' ' . $unit);
    }
}

function getFooterData($title, $data)
{
    if (!isset($data['documentData']['Unterschriftsbereich'][$title])) {
        return '';
    }

    return $data['documentData']['Unterschriftsbereich'][$title][0];
}

$employeeKeyNames = preg_grep('/^Mitarbeiter.*/', array_keys($data['documentData'] ?? []));

// fill array
foreach ($employeeKeyNames as $employeeKeyName) {
    $employee = $data['documentData'][$employeeKeyName];
    // skip empty employee entries
    if (!$employee)
        continue;
    $fromTime = $employee['von'][0] ?? '';
    $toTime = $employee['bis'][0] ?? '';
    $amount = $employee['Anzahl'][0] ?? '';
    $qualification = $employee['Mitarbeiter'][0] ?? '';

    $total = "";
    if (isset($employee['Gesamtstunden'])) {
        $total = $employee['Gesamtstunden'][0];

    } else {

        // calculate 0.25 rounded value based on amount, from and end times
        if ($fromTime && $toTime) {
            $fromDateTimeString = date('Y-m-d') . ' ' . $fromTime;
            $fromDateTime = DateTime::createFromFormat('Y-m-d H:i', $fromDateTimeString);
            // if fromTime is not in proper format (H:i , e.g. 08:00), we skip here
            if ($fromDateTime) {
                $toDateTimeString = date('Y-m-d') . ' ' . $toTime;
                $toDateTime = DateTime::createFromFormat('Y-m-d H:i', $toDateTimeString);
                // also check toDateTime
                if ($toDateTime) {
                    $difference = $toDateTime->diff($fromDateTime);
                    $differenceMinutes = ($difference->h * 60) + $difference->i;
                    if ($amount === "") {
                        $totalRaw = $differenceMinutes / 60.0;
                    } else {
                        $totalRaw = ($differenceMinutes * $amount) / 60.0;
                    }
                    $totalSteps = round($totalRaw / 0.25) * 0.25;
                    $total = PrintoutHelper::formatTimeNumber($totalSteps);
                }
            }
        }
    }

    $employeesTableData[] = [
        'anzahl' => $amount,
        'mitarbeiter' => $qualification,
        'von' => $fromTime,
        'bis' => $toTime,
        'gesamtstunden' => $total
    ];
}
?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Stundenlohnarbeiten</title>
</head>
<body>
<header class="flex-container">
    <div class="header-left">
        <img width="200px" height="auto" src='<?= $data['companyLogoUrl'] ?>' alt=""><br>
        <p class="mt-20">Datum</p>
        <p>
            <?php if (isset($data['documentData']['Datum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $newdate = new DateTime($data['documentData']['Datum'][0]);
                echo $newdate->format('d.m.Y');
            }
            ?>
        </p>

    </div>
    <div class="header-right">
        <address>
            <strong><?= $data['companyDetails']['displayName'] ?></strong><br><?= $data['companyDetails']['address'] ?>
            <br> <?= $data['companyDetails']['postcode'] . " " . $data['companyDetails']['city'] ?>
            <br><br><abbr title="Phone">Tel: </abbr><?= $data['companyDetails']['phoneNumber'] ?>
            <br><?= $data['companyDetails']['email'] ?>
        </address>
    </div>
</header>
<table class="header-table mt-50">
    <tr>
        <td>
            <h5>Stundenlohnarbeiten</h5>
        </td>
        <td>
            <h5><?php
                if (isset($data['documentData']['Tagesblattnummer'])) {
                    echo sprintf("<h5>Tagesblattnummer: %s</h5>", $data['documentData']['Tagesblattnummer'][0]);
                }
                ?>
            </h5>
        </td>
    </tr>
</table>
<hr>

<main>
    <p>Baustelle/Bereich: <?= $data['documentData']['Baustelle/Bereich'][0] ?? "" ?></p>
    <table class="main-table">
        <thead>
        <tr>
            <th>Anzahl</th>
            <th>Mitarbeiter</th>
            <th>Von</th>
            <th>Bis</th>
            <th>Gesamtstunden</th>
        </tr>
        </thead>
        <tbody>
        <?php
        if (isset($employeesTableData)) {
            foreach ($employeesTableData as $row) {
                echo "<tr>";
                foreach ($row as $cell) {
                    echo "<td>" . $cell . "</td>";
                }
                echo "</tr>";
            }
        }
        ?>
        </tbody>
    </table>
    <div class="below-main-table">
        <?php getDocumentData('Durchgeführte Arbeiten', $data, 'Durchgeführte Arbeiten: '); ?>
        <?php getDocumentData('Eingesetztes Material', $data, 'Eingesetztes Material:'); ?>
    </div>

    <h4>Eingesetztes Werkzeug / Bauschutt/ Sonstiges:</h4>
    <hr>
    <div class="flex-container" id="table-werkzeug">
        <table class="sub-table__left">
            <tbody>
            <?php
            printInnerData('Kompressor/ Presslufthammer in Stunden', 'h Kompressor/ Presslufthammer', $data);
            printInnerData('Elektrohammer in Stunden', 'h Elektrohammer', $data);
            printInnerData('Schnittfläche Wandsäge/Beton/Mauerwerk in m²', 'm² Schnittfläche Wandsäge/Beton/Mauerwerk', $data);
            printInnerData('LKW in Stunden', 'h LKW', $data);
            printInnerData('Stk. Kernbohrung (__Länge_____/Beton_MW)', 'Stk. Kernbohrung (Länge / Beton MW)', $data);
            ?>
            </tbody>
        </table>
        <table class="sub-table__right">
            <tbody>
            <?php
            printInnerData('Teleskoplader in Stunden', 'h Teleskoplader', $data);
            printInnerData('Bagger/ Radlader in Stunden', 'h Bagger/ Radlader', $data);
            printInnerData('Bauschutt/Restmüll in m³', 'm³ Bauschutt/Restmüll', $data);
            printInnerData('Sonstiges', '', $data, 'Sonstiges:');
            ?>
            </tbody>
        </table>
    </div>
</main>

<footer class="mt-20">
    <hr>
    <table class="footer-table-1">
        <thead>
        <tr>
            <th>Ort, Datum</th>
            <th>
                Unterschrift Auftraggeber
                <?php
                $nameAuftraggeber = getFooterData("Name Auftraggeber", $data);
                if ($nameAuftraggeber !== "") {
                    echo "<br>" . $nameAuftraggeber;
                }
                ?>
            </th>
            <th>Unterschrift Auftragnehmer Fecke</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td style="text-align: center">
                <?php
                $location = getFooterData('Ort', $data);
                $hasLocation = ($location !== "");
                if ($hasLocation) {
                    echo $location . ', ';
                }
                $rawDate = getFooterData('Datum', $data);
                if ($rawDate !== "") {
                    /** @noinspection PhpUnhandledExceptionInspection */
                    $date = new DateTime($rawDate);
                    echo $date->format('d.m.Y');
                }
                ?>
            </td>
            <td>
            </td>
            <td>
                <?php
                $unterschriftAuftragnehmer = getFooterData('Unterschrift Auftragnehmer', $data);
                if ($unterschriftAuftragnehmer != '') {
                    echo "<img width='100px' height='auto' src='" . $unterschriftAuftragnehmer . "' alt='Foto'>";
                } ?>
            </td>
        </tr>
        </tbody>
    </table>
    <hr>
</footer>
</body>
</html>
