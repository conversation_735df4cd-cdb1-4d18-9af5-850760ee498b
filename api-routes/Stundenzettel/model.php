<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Stundenzettel
{
    //todo make it so that $from and $to are optional
    public function getData($schemaId, $documentId, $personalNo, $from, $to, $ktrAanr): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        $data['Kosten Arbeitskleidung'] = $this->getKostenArbeitskleidung($schemaId, $documentId, $curl);

        $params = [
            'resnr' => $personalNo,
            'date' => $from,
            'to_date' => $to,
            'includeExpenseAllowanceInHoursEntry' => 1,
            'type' => 'approved'
        ];

        if ($ktrAanr) {
            $parts = explode('-', $ktrAanr);
            $params['ktr'] = $parts[0];
            $params['aanr'] = $parts[1];
        }

        $data['hours_data'] = $this->getHours($params, $curl);

        $data += $this->getGroupedStatisticData($data['hours_data']);

        $employee = PrintoutHelper::downloadEmployee($personalNo, $curl);

        // format birth date
        $birthDate = DateTime::createFromFormat("Y-m-d", $employee['birthDate']);
        if ($birthDate)
            $data['birth_date'] = $birthDate->format("d.m.Y");
        $data['pnr'] = $employee['pnr'];
        $data['print_name'] = $employee['displayName'];

        // get logo
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        // get companyName
        $data['companyDetails'] = $this->getSettingsInfo($curl);
        $data['documentationData'] = $this->getEmployeeDocumentation($personalNo, $curl);

        return $data;
    }

    private function getGroupedStatisticData($hours_data): array
    {
        // Generate data for the grouped statistics
        $date_flag = "";
        $ktr_flag = "";

        $annual_hours = $montly_hours = $ktr_hours = array();
        $annual_days = $montly_days = $ktr_days = array();
        $annual_bookings = $montly_bookings = $ktr_bookings = array();

        $annualBonusAmount = $monthlyBonusAmount = $ktrBonusAmount = array();
        $annualBonusBookings = $monthlyBonusBookings = $ktrBonusBookings = array();

        $totalBonusAmount = array();

        $stats_data = [];
        foreach ($hours_data as $row) {
            list($y, $m, $d) = explode('-', $row['date']);

            if ((($row['wageTypeExternal'] ?? "") == 'B' && $row['origin'] != 'TimeTrackEvent') || (strpos($row['ltext'], "via Expense Allowance @") == true)) {

                if (!isset($annualBonusAmount[$y][$row['wageTypeUnit']])) {
                    $annualBonusAmount[$y][$row['wageTypeUnit']] = 0;
                }
                if (!isset($monthlyBonusAmount[$y][$m][$row['wageTypeUnit']])) {
                    $monthlyBonusAmount[$y][$m][$row['wageTypeUnit']] = 0;
                }
                if (!isset($ktrBonusAmount[$row['ktr']])) {
                    $ktrBonusAmount[$row['ktr']] = 0;
                }
                if (!isset($annualBonusBookings[$y])) {
                    $annualBonusBookings[$y] = 0;
                }
                if (!isset($monthlyBonusBookings[$y][$m])) {
                    $monthlyBonusBookings[$y][$m] = 0;
                }
                if (!isset($ktrBonusBookings[$row['ktr']])) {
                    $ktrBonusBookings[$row['ktr']] = 0;
                }
                if (!isset($totalBonusAmount[$row['wageTypeUnit']])) {
                    $totalBonusAmount[$row['wageTypeUnit']] = 0;
                }
                $annualBonusAmount[$y][$row['wageTypeUnit']] += $row['std'];
                $monthlyBonusAmount[$y][$m][$row['wageTypeUnit']] += $row['std'];
                $ktrBonusAmount[$row['wageTypeUnit']] += $row['std'];
                $totalBonusAmount[$row['wageTypeUnit']] += $row['std'];
                $annualBonusBookings[$y] += 1;
                $monthlyBonusBookings[$y][$m] += 1;
                $ktrBonusBookings[$row['ktr']] += 1;
                $date_flag = $row['date'];
                $ktr_flag = $row['ktr'];
            } else {
                if (!isset($annual_hours[$y])) {
                    $annual_hours[$y] = 0;
                }
                if (!isset($montly_hours[$y][$m])) {
                    $montly_hours[$y][$m] = 0;
                }
                if (!isset($ktr_hours[$row['ktr']])) {
                    $ktr_hours[$row['ktr']] = 0;
                }
                if (!isset($annual_days[$y])) {
                    $annual_days[$y] = 0;
                }
                if (!isset($montly_days[$y][$m])) {
                    $montly_days[$y][$m] = 0;
                }
                if (!isset($ktr_days[$row['ktr']])) {
                    $ktr_days[$row['ktr']] = 0;
                }
                if (!isset($annual_bookings[$y])) {
                    $annual_bookings[$y] = 0;
                }
                if (!isset($montly_bookings[$y][$m])) {
                    $montly_bookings[$y][$m] = 0;
                }
                if (!isset($ktr_bookings[$row['ktr']])) {
                    $ktr_bookings[$row['ktr']] = 0;
                }
                $annual_hours[$y] += $row['std'];
                $montly_hours[$y][$m] += $row['std'];
                $ktr_hours[$row['ktr']] += $row['std'];
                $annual_days[$y] += $date_flag === $row['date'] ? 0 : 1;
                $montly_days[$y][$m] += $date_flag === $row['date'] ? 0 : 1;
                $ktr_days[$row['ktr']] += $date_flag === $row['date'] && $ktr_flag === $row['ktr'] ? 0 : 1;
                $annual_bookings[$y] += 1;
                $montly_bookings[$y][$m] += 1;
                $ktr_bookings[$row['ktr']] += 1;
                $date_flag = $row['date'];
                $ktr_flag = $row['ktr'];
            }

            // Send that data to the view
            //Bonuses
            $stats_data['annualBonusAmount'] = $annualBonusAmount;
            $stats_data['monthlyBonusAmount'] = $monthlyBonusAmount;
            $stats_data['ktrBonusAmount'] = $ktrBonusAmount;
            $stats_data['totalBonusAmount'] = $totalBonusAmount;
            $stats_data['annualBonusBookings'] = $annualBonusBookings;
            $stats_data['monthlyBonusBookings'] = $monthlyBonusBookings;
            $stats_data['ktrBonusBookings'] = $ktrBonusBookings;

            //Regular hours
            $stats_data['annual_hours'] = $annual_hours;
            $stats_data['montly_hours'] = $montly_hours;
            $stats_data['ktr_hours'] = $ktr_hours;
            $stats_data['annual_days'] = $annual_days;
            $stats_data['montly_days'] = $montly_days;
            $stats_data['ktr_days'] = $ktr_days;
            $stats_data['annual_bookings'] = $annual_bookings;
            $stats_data['montly_bookings'] = $montly_bookings;
            $stats_data['ktr_bookings'] = $ktr_bookings;
        }
        return $stats_data;
    }

    private function getHours($params, $curl)
    {
        $response = $curl->_simple_call('get', "/v1/hours/all",
            $params, PrintoutHelper::getHeadersForApiCalls());

        return json_decode($response, true);
    }

    private function getSettingsInfo($curl)
    {
        $response = $curl->_simple_call('get', "/v1/settings/info",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true);
    }

    private function getEmployeeDocumentation($personalNo, $curl)
    {
        $response = $curl->_simple_call('get', "/v1/documentation?employeeNo=$personalNo",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true);
    }

    private function getKostenArbeitskleidung($schemaId, $docId, $curl)
    {
        if ($docId == -1)
            return 0;
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $docId, $curl);
        foreach ($doc['fullDocument']['children'] as $child) {
            if ($child['title'] == 'Kosten Arbeitskleidung') {
                return $child['reportedValue'];
            }
        }
        return 0;
    }
}