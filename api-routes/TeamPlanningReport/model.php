<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_TeamPlanningReport
{
    /**
     * @throws Exception
     */
    public function getData(string $fromDate, string $toDate, string $employeeList): array
    {
        $dtFrom = new DateTime($fromDate);
        $dtTo = new DateTime($toDate);

        if ($dtTo < $dtFrom) {
            die_with_response_code(Response::BAD_REQUEST, "The toDate must be later than or equal to the fromDate.");
        }
        $daysDiff = $dtFrom->diff($dtTo)->days + 1;
        if ($daysDiff < 1 || $daysDiff > 28) {
            $message = "The date range must be between 1 and 28 days. Provided range: $daysDiff day(s).";
            die_with_response_code(Response::BAD_REQUEST, $message);
        }

        $templateData = [
            'from' => $dtFrom->format("d.m.Y"),
            'to' => $dtTo->format("d.m.Y")
        ];

        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        list($employeesResponse, $workingOrders) = $this->downloadData($curl, $fromDate, $toDate);

        $sortedEmployees = [];
        if ($employeesResponse['statusCode'] == 200) {
            foreach ($employeesResponse['response'] as $val) {
                $sortedEmployees[$val['pnr']] = $val['displayName'];
            }
        }

        $employeeListArray = array_filter(array_map('trim', explode(',', $employeeList)));
        $templateData['wos'] = [];

        if ($workingOrders['statusCode'] == 200) {
            foreach ($workingOrders['response'] as $v) {
                foreach ($v['staffPreplanned'] as $i => $staff) {
                    if (intval($staff) < 0) {
                        $v['staffPreplanned'][$staff] = "Partner: $staff";
                    } else {
                        $v['staffPreplanned'][$staff] = $sortedEmployees[$staff] ?? '';
                    }
                    unset($v['staffPreplanned'][$i]);
                }
                if (empty($employeeListArray)) {
                    if (isset($v['teamIdentification']['displayText'])) {
                        $templateData['wos'][$v['teamIdentification']['displayText']][] = $v;
                    }
                } else {
                    if (isset($v['teamIdentification']['id']) && in_array($v['teamIdentification']['id'], $employeeListArray)) {
                        $teamKey = $v['teamIdentification']['displayText'] ?? 'Unknown';
                        $templateData['wos'][$teamKey][] = $v;
                    }
                }
            }
        }

        $teamsInfo = [];
        if (!empty($employeeListArray) && $employeesResponse['statusCode'] == 200) {
            foreach ($employeesResponse['response'] as $emp) {
                if (in_array($emp['pnr'], $employeeListArray)) {
                    $teamsInfo[$emp['pnr']] = [
                        'id' => $emp['employeeNo'],
                        'name' => $emp['displayName'],
                        'profilePicture' => $emp['profilePicture']['thumb_path'] ?? ''
                    ];
                }
            }
        }
        $templateData['teamsInfo'] = $teamsInfo;

        $labelDetails = $this->downloadLabels($curl);
        $vehicleDetails = $this->downloadVehicleDetails($curl);
        ksort($templateData['wos']);
        $templateData['labels'] = $labelDetails;
        $templateData['labelsMap'] = $this->buildMap($labelDetails, 'id', 'children');
        $templateData['vehicles'] = $vehicleDetails;
        $templateData['vehiclesMap'] = $this->buildMap($vehicleDetails, 'rnr', 'children');

        foreach ($templateData['wos'] as &$tasks) {
            foreach ($tasks as &$task) {
                $task['resolvedLabels'] = $this->resolveItems($task['workingOrderLabelIds'] ?? [], $templateData['labelsMap']);
                $task['resolvedVehicles'] = $this->resolveItems($task['resourcesNonHr'] ?? [], $templateData['vehiclesMap']);
            }
        }
        unset($task, $tasks);

        $calendarData = $this->calculateCalendarHeaders($dtFrom, $dtTo);
        return array_merge($templateData, $calendarData);
    }

    /**
     * Calculate header cells, month groups, and week groups for the calendar.
     * @throws Exception
     */
    private function calculateCalendarHeaders(DateTime $fromDate, DateTime $toDate): array
    {
        $headerCells = [];
        $interval = new DateInterval('P1D');
        $toDateForPeriod = (clone $toDate);
        $toDateForPeriod->modify('+1 day');
        $dateRange = new DatePeriod($fromDate, $interval, $toDateForPeriod);
        foreach ($dateRange as $date) {
            $dayName = strtoupper($date->format('D')); // e.g. MON, TUE
            $dayNumber = $date->format('d');
            $headerCells[] = [
                'date' => $date->format('Y-m-d'),
                'dayName' => $dayName,
                'dayNumber' => $dayNumber,
                'class' => in_array($dayName, ['SAT', 'SUN']) ? 'weekend-col' : 'weekday-col'
            ];
        }

        // Pad with blank cells if provided range is less than 28 days.
        $currentCount = count($headerCells);
        if ($currentCount < 28) {
            $extraDays = 28 - $currentCount;
            for ($i = 0; $i < $extraDays; $i++) {
                $headerCells[] = [
                    'date' => '',
                    'dayName' => '',
                    'dayNumber' => '',
                    'class' => 'blank-col'
                ];
            }
        }

        // Group header cells by month.
        $months = [];
        foreach ($headerCells as $cell) {
            $monthKey = (!empty($cell['date'])) ? date('F Y', strtotime($cell['date'])) : '';
            if (!isset($months[$monthKey])) {
                $months[$monthKey] = [];
            }
            $months[$monthKey][] = $cell;
        }

        // Group cells by week and count the number of days per week.
        $weekGroups = [];
        $prevWeek = null;
        foreach ($headerCells as $cell) {
            $weekNum = (!empty($cell['date'])) ? (int)date('W', strtotime($cell['date'])) : '';
            if ($prevWeek !== $weekNum) {
                $weekGroups[] = ['week' => $weekNum, 'colspan' => 1];
                $prevWeek = $weekNum;
            } else {
                $lastIndex = count($weekGroups) - 1;
                $weekGroups[$lastIndex]['colspan']++;
            }
        }

        return [
            'headerCells' => $headerCells,
            'months' => $months,
            'weekGroups' => $weekGroups,
            'adjustedToDate' => $toDate,
            'toDateForPeriod' => $toDateForPeriod
        ];
    }

    private function buildMap(array $items, string $idKey, string $childrenKey): array
    {
        $map = [];
        foreach ($items as $item) {
            $map[(int)$item[$idKey]] = $item;
            if (isset($item[$childrenKey]) && is_array($item[$childrenKey])) {
                $map += $this->buildMap($item[$childrenKey], $idKey, $childrenKey);
            }
        }
        return $map;
    }

    private function resolveItems(array $ids, array $map): array
    {
        $resolved = [];
        foreach ($ids as $id) {
            if (isset($map[(int)$id])) {
                $resolved[] = $map[(int)$id];
            }
        }
        return $resolved;
    }

    private function downloadData($curl, $fromDate, $toDate)
    {
        $partial = "teamIdentification,staffPreplanned,resourcesNonHr,projectManager,projectName,shortDescription,uniqueKey,plannedDate,plannedStartTime,customerName,customerSalutation,projectNo,workingOrderNo,scaffoldItemId,projectSiteAddress1,projectSiteAddress2,projectSiteZipcode,projectSiteCity,taskName,projectColor,workingOrderLabelIds";
        $gteDate = date('Y-m-d', strtotime($fromDate));
        $lteDate = date('Y-m-d', strtotime($toDate));

        $employeeUrl = "v3/employees?showRelatedProject=false";
        $workingOrderUrl = "v3/workingorders?partial=$partial&filter[plannedDate][gte]=$gteDate&filter[plannedDate][lte]=$lteDate&filter[status][lt]=5";

        return $curl->_multi_call('get',
            [$employeeUrl, $workingOrderUrl],
            [], PrintoutHelper::getHeadersForApiCalls());
    }

    private function downloadVehicleDetails($curl): array
    {
        $url = "/v1/vehicles/select/all";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        $data = json_decode($response, true);
        return $data ?? [];
    }

    private function downloadLabels($curl): array
    {
        $url = "/v1/labels";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        $data = json_decode($response, true);
        return $data ?? [];
    }
}
