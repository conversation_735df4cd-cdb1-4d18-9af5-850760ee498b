<?php
namespace UnfallanzeigeBgBau;

use C_UnfallanzeigeBgBau;
use PrintoutHelper;

/** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_UnfallanzeigeBgBau())->getData($_GET['schemaId'], $_GET['documentId']);
}
function renderCheckbox($data, $key, $value): void
{
    echo '<span class="checkbox-size">';
    echo ($data[$key] ?? '') === $value ? '&#9745;' : '&#9744;';
    echo " $value</span>";
}

function getDateComponents($dateString, $type = 'full'): array
{
    if (empty($dateString)) {
        if ($type === 'time') {
            return ['hour' => '', 'minute' => ''];
        }
        return [
            'day' => '',
            'month' => '',
            'year' => '',
            'hour' => '',
            'minute' => ''
        ];
    }
    $timestamp = strtotime($dateString);
    if ($type === 'time') {
        return [
            'hour' => date('H', $timestamp),
            'minute' => date('i', $timestamp)
        ];
    }
    return [
        'day' => date('d', $timestamp),
        'month' => date('m', $timestamp),
        'year' => date('Y', $timestamp),
        'hour' => date('H', $timestamp),
        'minute' => date('i', $timestamp)
    ];
}

function getTimeComponents(string $timeString): array
{
    if (!empty($timeString) && str_contains($timeString, ':')) {
        $parts = explode(':', $timeString, 2);
        return ['hour' => $parts[0], 'minute' => $parts[1]];
    }
    return ['hour' => '', 'minute' => ''];
}

?>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>UnfallanzeigeBgBau</title>
</head>
<body>
<div class="page">
    <!-- Header Table -->
    <table class="header-table">
        <tr>
            <td colspan="2">
                <div><strong>Verfasser</strong></div>
                <div>QMB</div>
                <div><strong>Veröffentlichungsdatum</strong></div>
                <div>14.09.2022</div>
            </td>
            <td colspan="2">
                <div><strong>Ansprechpartner</strong></div>
                <div>Personalwesen</div>
                <div><strong>Nachweis Ablage</strong></div>
                <div>Personalakte</div>
            </td>
            <td colspan="2">
                <div><strong>Freigegeben durch</strong></div>
                <div>Kaufm. Leitung</div>
                <div><strong>Revision</strong></div>
                <div>001</div>
            </td>
            <td colspan="4" style="text-align: right;">
                <img src="<?php echo PrintoutHelper::translateLocalPathToServerPathFromRoot('img/schaferLogo.png') ?>"
                     alt="SCHÄFER Logo" class="logo">
            </td>
        </tr>
    </table>

    <!-- Main Content Table -->
    <table class="main-table">
        <tr>
            <td colspan="12" class="border-none heading-title">
                <div class="heading-text">Unfallanzeige</div>
            </td>
        </tr>
        <tr>
            <td colspan="8" class="border-none-bottom border-none-right">
                <strong>1 Name und Anschrift des Unternehmers</strong><br>
                <?= $data['info']['address']['name'] ?? '' ?><br>
                <?= $data['info']['address']['address'] ?? '' ?><br>
                <?= $data['info']['address']['postcode'] ?? '' ?>
                <?= $data['info']['address']['city'] ?? '' ?><br>
                Tel: <?= $data['info']['address']['phoneNumber'] ?? '' ?>
                /Fax: <?= $data['info']['address']['faxNumber'] ?? '' ?><br>
            </td>
            <td colspan="4" class="border-none-bottom border-none-left"
                style="width: 300px; padding-top: 5px; padding-right:0;">
                <strong>2 Unfallversicherungsnummer des Unfallversicherungsträgers</strong><br>
                <table class="number-table" style="width: 100%;">
                    <tr>
                        <td class="border-none-top">1</td>
                        <td class="border-none-top">0</td>
                        <td class="border-none-top">6</td>
                        <td class="border-none-top">0</td>
                        <td class="border-none-top">6</td>
                        <td class="border-none-top">9</td>
                        <td class="border-none-top">3</td>
                        <td class="border-none-top">7</td>
                        <td class="border-none-top">8</td>
                        <td class="border-none-top">7</td>
                        <td class="border-none-top">7</td>
                        <td class="border-bottom-only"></td>
                        <td class="border-bottom-only"></td>
                        <td class="border-bottom-only"></td>
                        <td class="border-bottom-only"></td>
                        <td class="border-bottom-only"></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="12" class="border-none-top">
                <strong style="padding-bottom: 5px">3 Empfänger</strong><br>
                <div class="corners">
                    <div class="top left"></div>
                    <div class="top right"></div>
                    <div class="bottom right"></div>
                    <div class="bottom left"></div>
                    Berufsgenossenschaft der<br>
                    Bauwirtschaft<br>
                    Postfach GE<br>
                    80267 München
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <strong>4 Name, Vorname des Versicherten </strong><br>
                <div class="text-container">
                    <?php
                    $parts = [];
                    if (isset($data['employee']['lastName'])) {
                        $parts[] = $data['employee']['lastName'];
                    }
                    if (isset($data['employee']['firstName'])) {
                        $parts[] = $data['employee']['firstName'];
                    }
                    echo implode(', ', $parts);
                    ?>
                </div>
            </td>
            <td colspan="1">
                <strong>5 Geburtsdatum</strong><br>
                <div class="text-container">
                    <?php
                    echo isset($data['employee']['birthDate']) ? date('d.m.Y', strtotime($data['employee']['birthDate'])) : '';
                    ?>
                </div>
            </td>
            <td colspan="1">
                <strong>Tag</strong><br>
                <div class="text-container">
                    <?= isset($data['employee']['birthDate']) ? date('d', strtotime($data['employee']['birthDate'])) : ''; ?>
                </div>
            </td>
            <td colspan="1">
                <strong>Monat</strong><br>
                <div class="text-container">
                    <?= isset($data['employee']['birthDate']) ? date('n', strtotime($data['employee']['birthDate'])) : ''; ?>
                </div>
            </td>
            <td colspan="1">
                <strong>Jahr</strong><br>
                <div class="text-container">
                    <?= isset($data['employee']['birthDate']) ? date('Y', strtotime($data['employee']['birthDate'])) : ''; ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>6 Straße, Hausnummer</strong><br>
                <div class="text-container">
                    <?= $data['employee']['address'] ?? '' ?>
                </div>
            </td>
            <td colspan="5">
                <strong>Postleitzahl</strong><br>
                <div class="text-container">
                </div>
                <?= $data['employee']['zipCode'] ?? '' ?>
            </td>
            <td colspan="5">
                <strong>Ort</strong><br>
                <div class="text-container">
                    <?= $data['employee']['city'] ?? '' ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>7 Geschlecht</strong><br>
                <div class="text-container">
                    <span><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Mitarbeiter Geschlecht', 'männlich'); ?></span>
                    <span class="left-space"><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Mitarbeiter Geschlecht', 'weiblich'); ?></span>
                </div>
            </td>
            <td colspan="5">
                <strong>8 Staatsangehörigkeit</strong><br>
                <div class="text-container">
                    <?= $data['employee']['nationality'] ?? '' ?>
                </div>
            </td>
            <td colspan="5">
                <strong>9 Leiharbeitnehmer</strong><br>
                <div class="text-container">
                <span>
                    <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Leiharbeitnehmer', 'ja'); ?>
                </span>
                    <span class="left-space">
                    <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Leiharbeitnehmer', 'nein'); ?>
                </span>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <strong>10 Auszubildender</strong><br>
                <div class="text-container">
                <span>
                    <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Auszubildender', 'ja'); ?>
                </span>
                    <span class="left-space">
                    <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Auszubildender', 'nein'); ?>
                </span>
                </div>
            </td>
            <td colspan="10" style="width: 300px">
                <table class="checkboxes-table" style="width: 100%;border-spacing: 0;">
                    <tr>
                        <td class="border-none" style="padding: 0"><strong>11 Ist der Versicherte </strong></td>
                        <td class="border-none" style="padding: 0">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Ist der Versicherte', 'Unternehmer'); ?>
                        </td>
                        <td class="border-none" style="padding: 0">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Ist der Versicherte', 'Ehegatte des Unternehmers'); ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="border-none"></td>
                        <td class="border-none" style="padding: 0">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Ist der Versicherte', 'mit dem Unternehmer verwandt'); ?>
                        </td>
                        <td class="border-none" style="padding: 0">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Ist der Versicherte', 'Gesellschafter/ Geschäftsführer'); ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="padding-bottom: 0">
                <strong>12 Anspruch auf Entgeltfortzahlung</strong><br>
                <table class="work-table" style="padding-top: 5px;">
                    <tr>
                        <td style="vertical-align: bottom" class="border-none"> besteht für</td>
                        <?php
                        $value = $data['schema']['UnfallanzeigeBgBau Anspruch auf Entgeltfortzahlung'] ?? 0;
                        $formattedValue = sprintf("%02d", (int)$value);
                        ?>
                        <td style="text-align: center;padding: 10px;border-right: none; border-bottom: none;"><?= $formattedValue[0] ?></td>
                        <td style="text-align: center;padding: 10px; border-bottom: none;"><?= $formattedValue[1] ?></td>
                        <td style="vertical-align: bottom" class="border-none">
                            Wochen
                        </td>
                    </tr>
                </table>
            </td>
            <td colspan="10">
                <strong>13 Krankenkasse des Versicherten (Name, PLZ, Ort)</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Krankenkasse des Versicherten'] ?? '' ?></div>
            </td>
        </tr>
        <tr>
            <td colspan="2" style="width: 300px; padding-bottom: 0;">
                <strong>14 Tödlicher Unfall</strong><br>
                <table class="tag-table" style="padding-top: 5px; width: 100%">
                    <tr style="height: 20px;">
                        <td class="border-right-only" style="width: 50%">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Tödlicher Unfall', 'ja'); ?>
                        </td>
                        <td class="border-none" style="width: 50%">
                            <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Tödlicher Unfall', 'nein'); ?>
                        </td>
                    </tr>
                    <tr style="height: 10px;">
                        <td class="border-right-only" style="width: 50%"></td>
                        <td class="border-none" style="width: 50%"></td>
                    </tr>
                    <tr style="height: 20px;">
                        <td class="border-right-only" style="width: 50%"></td>
                        <td class="border-none" style="width: 50%"></td>
                    </tr>
                </table>
            </td>
            <td colspan="6" style="padding-bottom:0; width: 300px">
                <strong>15 Unfallzeitpunkt</strong><br>
                <table class="tag-table" style="padding-top: 5px">
                    <tr style="height: 20px;">
                        <td colspan="2" class="border-right-only" style="text-align: center">Tag</td>
                        <td colspan="2" class="border-right-only" style="text-align: center">Monat</td>
                        <td colspan="2" class="border-right-only" style="text-align: center">Jahr</td>
                        <td colspan="2" class="border-right-only" style="text-align: center">Stunde</td>
                        <td colspan="2" class="border-none" style="text-align: center">Minute</td>
                    </tr>
                    <tr style="height: 10px;">
                        <td colspan="2" class="border-right-only"></td>
                        <td colspan="2" class="border-right-only"></td>
                        <td colspan="2" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-none"></td>
                    </tr>
                    <tr style="height: 20px;">
                        <?php
                        $dateComponents = getDateComponents($data['schema']['UnfallanzeigeBgBau Unfallzeitpunkt Datum'] ?? '');
                        $timeComponents = getTimeComponents($data['schema']['UnfallanzeigeBgBau Unfallzeitpunkt Zeit'] ?? '');
                        ?>
                        <td colspan="2" class="border-right-only"><?= $dateComponents['day']; ?></td>
                        <td colspan="2" class="border-right-only"><?= $dateComponents['month']; ?></td>
                        <td colspan="2" class="border-right-only"><?= $dateComponents['year']; ?></td>
                        <td colspan="1" class="border-right-only"><?= $timeComponents['hour']; ?></td>
                        <td colspan="1" class="border-right-only">Std.</td>
                        <td colspan="1" class="border-right-only"><?= $timeComponents['minute']; ?></td>
                        <td colspan="1" class="border-none">min.</td>
                    </tr>
                </table>
            </td>
            <td colspan="4" style="padding-bottom:0">
                <strong>16 Unfallort (genaue Orts- und Straßenangabe mit PLZ)</strong>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Unfallort'] ?? '' ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="12">
                <strong>17 Ausführliche Schilderung des Unfallhergangs (Verlauf, Bezeichnung des Betriebsteils, ggf.
                    Beteiligung von Maschinen, Anlagen,
                    Gefahrstoffen)</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Ausführliche Schilderung des Unfallhergangs'] ?? '' ?>
                </div>
                <br>
                <span> Die Angaben beruhen auf der Schilderung</span>
                <span><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Die Angaben beruhen auf der Schilderung', 'des Versicherten'); ?></span>
                <span class="left-space"><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Die Angaben beruhen auf der Schilderung', 'anderer Personen'); ?></span>

            </td>
        </tr>
        <tr>
            <td colspan="4">
                <strong>18 Verletzte Körperteile</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Verletzte Körperteile'] ?? '' ?>
                </div>
            </td>
            <td colspan="8">
                <strong>19 Art der Verletzung</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Art der Verletzung'] ?? '' ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="8" class="border-none-right">
                <strong>20 Wer hat von dem Unfall zuerst Kenntnis genommen? (Name, Anschrift des Zeugen)</strong>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Wer hat von dem Unfall zuerst Kenntnis genommen?'] ?? '' ?>
                </div>
            </td>
            <td colspan="4" class="border-none-left">
                <strong>War diese Person Augenzeuge</strong>
                <br>
                <span style="padding-top: 5px"><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau War diese Person Augenzeuge', 'ja'); ?></span>
                <span class="left-space"><?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau War diese Person Augenzeuge', 'nein'); ?></span>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <strong>21 Name und Anschrift des erstbehandelnden Arztes/Krankenhauses</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Name und Anschrift des erstbehandelnden Arztes/Krankenhauses'] ?? '' ?>
                </div>
            </td>
            <td colspan="4" style="padding-bottom: 0">
                <strong>22 Beginn und Ende der Arbeitszeit des Versicherten</strong><br>
                <table class="hours-table" style="padding-top: 5px;">
                    <tr>
                        <td colspan="2" class="border-none"></td>
                        <td colspan="2" class="border-none-top border-none-bottom">Stunde</td>
                        <td colspan="2" class="border-right-only">Minute</td>
                        <td colspan="2" class="border-none"></td>
                        <td colspan="2" class="border-none-top border-none-bottom">Stunde</td>
                        <td colspan="2" class="border-none">Minute</td>
                    </tr>
                    <tr>
                        <td colspan="2" class="border-none"></td>
                        <td colspan="1" class="border-none-top border-none-bottom"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="2" class="border-none"></td>
                        <td colspan="1" class="border-none-top border-none-bottom"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-right-only"></td>
                        <td colspan="1" class="border-none"></td>
                    </tr>
                    <tr>
                        <?php
                        $timeComponentsBegin = getDateComponents($data['schema']['UnfallanzeigeBgBau Beginn der Arbeitszeit des Versicherten'] ?? '', 'time');
                        $timeComponentsEnd = getDateComponents($data['schema']['UnfallanzeigeBgBau Ende der Arbeitszeit des Versicherten'] ?? '', 'time'); ?>
                        <td colspan="2" class="border-none">Begin</td>
                        <td colspan="1"
                            class="border-none-top border-none-bottom"><?= $timeComponentsBegin['hour']; ?></td>
                        <td colspan="1" class="border-right-only">Std.</td>
                        <td colspan="1" class="border-right-only"><?= $timeComponentsBegin['minute']; ?></td>
                        <td colspan="1" class="border-right-only">min</td>
                        <td colspan="2" class="border-none">Ende</td>
                        <td colspan="1"
                            class="border-none-top border-none-bottom"><?= $timeComponentsEnd['hour']; ?></td>
                        <td colspan="1" class="border-right-only">Std.</td>
                        <td colspan="1" class="border-right-only"><?= $timeComponentsEnd['minute']; ?></td>
                        <td colspan="1" class="border-none">min</td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="6">
                <strong>23 Zum Unfallzeitpunkt beschäftigt/tätig als</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Zum Unfallzeitpunkt beschäftigt/tätig als'] ?? '' ?>
                </div>
            </td>
            <td colspan="4">
                <strong>24 Seit wann bei dieser Tätigkeit</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau Seit wann bei dieser Tätigkeit'] ?? '' ?>
                </div>
            </td>
            <td colspan="1">
                <?php $dateComponents1 = getDateComponents($data['schema']['UnfallanzeigeBgBau Datum - Seit wann bei dieser Tätigkeit'] ?? ''); ?>
                <strong>Monat</strong><br>
                <div class="text-container"><?= $dateComponents1['month']; ?></div>
            </td>
            <td colspan="1">
                <strong>Jahr</strong><br>
                <div class="text-container"><?= $dateComponents1['year']; ?></div>
            </td>
        </tr>
        <tr>
            <td colspan="12">
                <strong>25 In welchem Teil des Unternehmens ist der Versicherte ständig tätig?</strong><br>
                <div class="text-container">
                    <?= $data['schema']['UnfallanzeigeBgBau In welchem Teil des Unternehmens ist der Versicherte ständig tätig?'] ?? '' ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="9">
                <strong>26 Hat der Versicherte die Arbeit eingestellt?</strong>
                <span>
                <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Hat der Versicherte die Arbeit eingestellt?', 'nein'); ?>
            </span>
                <span>
                <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Hat der Versicherte die Arbeit eingestellt?', 'sofort'); ?>
            </span>
                <span style="padding-left: 20px">
                <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Hat der Versicherte die Arbeit eingestellt?', 'später am'); ?>
            </span>
            </td>
            <?php $dateComponents2 = getDateComponents($data['schema']['UnfallanzeigeBgBau Datum'] ?? ''); ?>
            <?php $timeComponents2 = getTimeComponents($data['schema']['UnfallanzeigeBgBau Uhrzeit'] ?? ''); ?>

            <td colspan="1">
                <strong>Tag</strong><br>
                <div class="text-container"><?= $dateComponents2['day']; ?></div>
            </td>
            <td colspan="1">
                <strong>Monat</strong><br>
                <div class="text-container"><?= $dateComponents2['month']; ?></div>
            </td>
            <td colspan="1">
                <strong>Stunde</strong><br>
                <div class="text-container">
                    <span class="border-right-only"><?= $timeComponents2['hour']; ?></span>
                    <span><?= $timeComponents2['minute']; ?></span>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="9">
                <strong>27 Hat der Versicherte die Arbeit wieder aufgenommen? </strong>
                <span>
                <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Hat der Versicherte die Arbeit wieder aufgenommen?', 'nein'); ?>
            </span>
                <span class="left-space">
                <?php renderCheckbox($data['schema'], 'UnfallanzeigeBgBau Hat der Versicherte die Arbeit wieder aufgenommen?', 'ja, am'); ?>
            </span>
            </td>
            <?php $dateComponents3 = getDateComponents($data['schema']['UnfallanzeigeBgBau Datum - Hat der Versicherte die Arbeit wieder aufgenommen'] ?? ''); ?>

            <td colspan="1">
                <strong>Tag</strong><br>
                <div class="text-container"><?= $dateComponents3['day']; ?></div>
            </td>
            <td colspan="1">
                <strong>Monat</strong><br>
                <div class="text-container"><?= $dateComponents3['month']; ?></div>
            </td>
            <td colspan="1">
                <strong>Jahr</strong><br>
                <div class="text-container"><?= $dateComponents3['year']; ?></div>
            </td>
        </tr>
        <tr>
            <td colspan="3" class="border-none-right">
                <strong> 28 Datum </strong>
                <br>
                <span>
                <?php
                if (isset($data['schema']['UnfallanzeigeBgBau Datum'])) {
                    echo date('d.m.Y', strtotime($data['schema']['UnfallanzeigeBgBau Datum']));
                }
                ?>
            </span>
            </td>
            <td colspan="3" class="border-bottom-only">
                <strong>Unternehmer/Bevollmächtigter </strong>
                <br>
                <span><?= $data['schema']['UnfallanzeigeBgBau Unternehmer/Bevollmächtigter'] ?? '' ?></span>
            </td>
            <td colspan="3" class="border-bottom-only">
                <strong>Betriebsrat (Personalrat) </strong>
                <br>
                <span><?= $data['schema']['UnfallanzeigeBgBau Betriebsrat (Personalrat)'] ?? '' ?></span>
            </td>
            <td colspan="3" class="border-none-left">
                <strong>Telefon-Nr. für Rückfragen (Ansprechpartner) </strong>
                <br>
                <span><?= $data['schema']['UnfallanzeigeBgBau Telefon-Nr. für Rückfragen (Ansprechpartner)'] ?? '' ?></span>
            </td>
        </tr>
    </table>
</div>
</body>
</html>