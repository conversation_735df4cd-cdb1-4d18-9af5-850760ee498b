<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';
require_once __DIR__ . '/../../utils/BundesInnungGeruestbauHelper.php';

class C_VerguetungZusaetzlicheLeistungen
{
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument((int)$schemaId, (int)$documentId, $curl);
        $schemaChildren = PrintoutHelper::downloadSchema($schemaId, $curl)['children'];
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], false, $schemaChildren);
        $data['documentDate'] = $doc['fullDocument']['documentCreatedOn'];
        $projectNo = $doc['fullDocument']['documentRelKey1'];
        return array_merge(BundesInnungGeruestbauHelper::downloadCommonDataForHeader($curl, $projectNo), $data);
    }
}
