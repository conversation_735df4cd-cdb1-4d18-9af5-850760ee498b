<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_ZB2CocDokument
{
    /**
     * @throws Exception
     */
    public function getData($schemaId, $documentId): array
    {
        $filePath = __DIR__ . '/data.json';
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }
        $data = json_decode(file_get_contents($filePath), true);

        $templateData = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $rnr = $document['fullDocument']['documentRelKey1'];
        $templateData['vehicleResource'] = $this->getVehicleResource($rnr, $curl);
        $templateData['vehicleProperty'] = $this->getVehicleProperty($rnr, $curl);
        $cocValues = $this->getAllCocValues($data);
        $preparedData = $this->prepareData($data, $cocValues);

        $templateData['firstPage'] = $this->prepareFirstPage($preparedData);
        $templateData['otherPages'] = $this->prepareOtherPages($preparedData);
        $templateData['headlines'] = $this->getHeadlines($data);

        return $templateData;
    }

    private function getVehicleResource($rnr, $curl)
    {
        return json_decode($curl->_simple_call('get', "v1/zb2vehicles/select_resource/$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function getVehicleProperty($rnr, $curl)
    {
        $response = json_decode($curl->_simple_call('get', "v1/zb2vehicles/properties?rnr=$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);

        if ($response)
            return end($response);

        return [];
    }

    private function getAllCocValues($data): array
    {
        $coc = [];
        foreach ($data as $child) {
            if ($child['type'] == 'headline')
                continue;
            $coc[] = $this->getFirstWord($child['title']);
        }

        return array_unique($coc);
    }

    private function prepareData($data, $cocValues): array
    {
        $returnData = [];
        foreach ($cocValues as $coc) foreach ($data as $da) {
            if ($da['type'] == 'headline')
                continue;
            $dataCoc = $this->getFirstWord($da['title']);

            if ($dataCoc === $coc) {
                $temp = [
                    'jsonKey' => $da['jsonKey'],
                    'title' => $da['title'],
                    'coc' => $coc,
                    'unit' => $da['unit']
                ];
                $returnData[$coc][] = $temp;
            }
        }
        return $returnData;
    }

    private function getFirstWord($data)
    {
        $exploded = explode(" ", $data);
        return $exploded[0];
    }

    private function prepareFirstPage($data): array
    {
        $returnData = [];
        foreach ($data as $key => $value) {
            if (intval($key) == 0) {
                $returnData[$key] = $value;
            }
        }
        return $returnData;
    }

    private function prepareOtherPages($data): array
    {
        $returnData = [];
        foreach ($data as $key => $value) {
            if (intval($key) != 0) {
                $returnData[$key] = $value;
            }
        }
        return $returnData;
    }

    private function getHeadlines($data): array
    {
        $headlines = [];

        $lastChild = null;
        foreach ($data as $child) {
            if ($child['type'] === "headline") {
                $headlines[explode(" ", $lastChild['title'])[0]] = $child['title'];
            }
            $lastChild = $child;
        }

        return $headlines;
    }
}