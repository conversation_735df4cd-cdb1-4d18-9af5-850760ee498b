<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ZB2CocDokument())->getData($_GET['schemaId'],$_GET['documentId']);
}

function displayFirstPage($data): string
{
    $row = "";
    foreach ($data['firstPage'] as $item) {
        $hideCoc = false;
        foreach ($item as $da) {
            $title = returnCorrectTitle($da);
            $apiData = getApiData($da['jsonKey'], $data, 'property');

            if (!empty($apiData)) {
                $row .= displayRow($hideCoc, $da, $title, $apiData);
            }
            $hideCoc = true;
        }
    }
    return $row;
}

function displayOtherPages($data): string
{
    $row = "";
    $existingHeadlines = [];
    foreach ($data['otherPages'] as $item) {
        $hideCoc = false;
        foreach ($item as $da) {
            $title = returnCorrectTitle($da);
            $apiData = getApiData($da['jsonKey'], $data, 'property');

            if (!empty($apiData)) {
                $newHeadline = getHeadline($da['coc'], $data['headlines'], $existingHeadlines);
                if ($newHeadline != "") {
                    $row .= sprintf("<tr><td class='headline-text' colspan='3'>%s</td></tr>", $newHeadline);
                    $existingHeadlines[] = $newHeadline;
                }

                $row .= displayRow($hideCoc, $da, $title, $apiData);
            }
            $hideCoc = true;
        }
    }
    return $row;
}

function displayRow($hideCoc, $da, $title, $apiData): string
{
    $row = "<tr>";
    if (!$hideCoc) {
        $row .= sprintf("<td>%s</td>", $da['coc']);
    } else {
        $row .= "<td>&nbsp;</td>";
    }

    $row .= sprintf("<td>%s</td>", $title);
    $row .= sprintf("<td>%s</td>", $apiData);
    $row .= sprintf("<td>%s</td>", $da['unit']);
    $row .= "</tr>";

    return $row;
}

function returnCorrectTitle($data): string
{
    $words = explode(" ", $data['title']);
    $remainingWords = array_slice($words, 1);
    return implode(" ", $remainingWords);
}

function getApiData($jsonKey, $data, $flag)
{
    if ($flag === 'property') {
        if (isset($data['vehicleProperty'][$jsonKey])) {
            return $data['vehicleProperty'][$jsonKey];
        }
    } elseif ($flag === 'resource') {
        if (isset($data['vehicleResource'][$jsonKey])) {
            return $data['vehicleResource'][$jsonKey];
        }
    }

    return "";
}

function getHeadline($coc, $headlines, $existingHeadlines): string
{
    foreach ($headlines as $key => $value) {
        $coc = floatval($coc);
        $headlineCoc = floatval($key);
        if ($coc > $headlineCoc && $value != in_array($value, $existingHeadlines)) {
            return $value;
        }
    }
    return "";
}

?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>ZB2CocDokument</title>
</head>
<body>
<div style="text-align: center; font-weight: bold">
    <h1 style="font-size: 20px">Übereinstimmungsbescheinigung</h1>
    <h2>VOLLSTÄNDIGE FAHRZEUGE</h2>
</div>

<table class="first-table">
    <tr>
        <td colspan="2">Der Unterzeichnete bestätigt hiermit, dass das Fahrzeug:</td>
        <td colspan="2">&nbsp;</td>
    </tr>
    <?= displayFirstPage($data) ?>
</table>

<p class="text-before-signature">
    mit dem in der am 15.04.2021 erteilten Genehmigung e1*2007/46*1489*04 beschriebenen Typ in jeder
    Hinsicht übereinstimmt und zur fortwährenden Teilnahme am Straßenverkehr in Mitgliedsstaaten mit Rechts-/
    Linksverkehr, in denen metrische Einheiten/Einheiten des englischen Maßsystems (Imperial System) für das
    Geschwindigkeitsmessgerät und metrische Einheiten/Einheiten des englischen Maßsystems (Imperial System)
    für den Kilometerzähler (falls zutreffend) verwendet werden, zugelassen werden kann.
</p>

<table class="signature-table">
    <tr>
        <td>
            <div>
                <hr class="short-hr">
            </div>
            <div class="pt-5">(Ort, Datum)</div>
        </td>
        <td>
            <div>
                <hr class="short-hr">
            </div>
            <div class="pt-5">(Unterschrift)</div>
        </td>
    </tr>
</table>

<div class="page-break"></div>

<table class="second-page-table" style="border-collapse: collapse; width: 100%">
    <?= displayOtherPages($data) ?>
</table>
</body>
</html>