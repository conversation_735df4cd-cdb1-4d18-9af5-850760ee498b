<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */

require_once __DIR__ . '/../../printout.helper.php';

class C_ZB2Druckzuordnung
{
    public function getData(string $schemaId, string $documentId): array
    {
        $schemaId = (int)$schemaId;
        $documentId = (int)$documentId;
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $fullDocument = $document['fullDocument'];
        $relType = $fullDocument['documentRelType'];
        if ($relType !== 'resource') {
            die_with_response_code(Response::BAD_REQUEST,
                "This printout is only allowed for vehicle documentation, but got relType: $relType");
        }
        $rnr = $fullDocument['documentRelKey1'];
        $data = [];
        $data['vehicleResource'] = $this->downloadVehicleResource($rnr, $curl);
        $data['vehicleResourceProperties'] = $this->downloadVehicleResourceProperties($rnr, $curl);
        $data['manufacturerShort'] = PrintoutHelper::downloadInfoSettings($curl)['address']['name'] ?? "";
        return $data;
    }

    private function downloadVehicleResource(int|string $rnr, PrintoutCurl $curl)
    {
        return json_decode($curl->_simple_call('get', "v1/zb2vehicles/select_resource/$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    private function downloadVehicleResourceProperties(int|string $rnr, PrintoutCurl $curl)
    {
        //?partial doesnt work on this endpoint
        return json_decode($curl->_simple_call('get', "/v1/zb2vehicles/$rnr/properties",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}