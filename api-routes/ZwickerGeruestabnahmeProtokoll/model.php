<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_ZwickerGeruestabnahmeProtokoll
{
    public function getData($schemaId, $documentId): array
    {
        $templateData = [];
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);
        $templateData['mappedChildren'] = PrintoutHelper::mapDocumentChildrenToStableNumberedIndices($schema, $doc, $curl);
        $templateData['project'] = PrintoutHelper::downloadProject($doc['documentRelKey1'], 'projectName', $curl);
        $templateData['fullDocument'] = $doc['fullDocument'];
        $chefmonteurPnr = $templateData['mappedChildren'][55]['value'];
        $kopasPnr = $templateData['mappedChildren'][57]['value'];
        $templateData['Chefmonteur'] = empty($chefmonteurPnr) ? '' : $this->getEmployee($chefmonteurPnr, $curl);
        $templateData['KOPAS'] = empty($kopasPnr) ? '' : $this->getEmployee($kopasPnr, $curl);

        return $templateData;
    }

    private function getEmployee($pnr, $curl): string
    {
        $employee = PrintoutHelper::downloadEmployee($pnr, $curl);
        if ($employee)
            return $employee['displayName'];

        return '';
    }
}

