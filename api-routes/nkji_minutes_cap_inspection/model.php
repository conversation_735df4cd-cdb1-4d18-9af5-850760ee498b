<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_NKJI_Minutes_Cap_Inspection
{
    public static function getDisplayNameForEmployee($pnr, $curl): string
    {
        if ($pnr) {
            return PrintoutHelper::downloadEmployee($pnr, $curl)['displayName'];
        }
        return "";
    }

    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);

        $mappedDocument = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);
        $displayInsideChildren = $this->getDisplayInside($doc['fullDocument']);
        $mappedDocument = array_merge($mappedDocument, $displayInsideChildren);

        $data['Извършил проверката:'] =
            self::getDisplayNameForEmployee($mappedDocument['5. Извършил проверката'], $curl);

        $table = [];
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);

        foreach ($schema['children'] as $child) {
            $title = $child['title'];

            $foundDocChild = null;
            foreach ($doc['fullDocument']['children'] as $docChild) {
                if ($child['id'] === $docChild['id']) {
                    $foundDocChild = $docChild;
                    break;
                }
            }

            if (is_numeric($title[0])) {
                $number = explode(" ", $title, 2);
                if ($foundDocChild) {
                    $value = $foundDocChild['reportedValue'];

                    if ($title === '5. Извършил проверката') {
                        $value = $data['Извършил проверката:'];
                    } else if (strtoupper($child['type']) === SchemaTypes::DATE) {
                        $value = PrintoutHelper::dateConverter($value, 'd/m/Y');
                    }

                } else {
                    $value = '';
                }

                $table[] = [
                    'number' => $number[0],
                    'title' => $number[1],
                    'value' => $value
                ];

            } else if ($title === 'Статус') {
                $val = ($foundDocChild && $foundDocChild['reportedValue']) ?
                    $foundDocChild['reportedValue'] : '';
                $table[] = [
                    'number' => '',
                    'title' => $title,
                    'value' => $val
                ];
            }
        }

        $data['table'] = $table;
        $data['mappedDocument'] = $mappedDocument;
        $data['documentId'] = $doc['fullDocument']['documentId'];
        $data['author'] = self::getDisplayNameForEmployee($doc['fullDocument']['author'], $curl);

        return $data;
    }

    public function getDisplayInside($document): array
    {
        if (!isset($document['children']))
            return [];
        $data = [];
        foreach ($document['children'] as $parent) {
            foreach ($document['children'] as $child) {
                if (isset($child['displayInside']) && $parent['id'] === $child['displayInside']) {
                    $title = $parent['title'] . ' ' . $child['title'];
                    $data[$title] = $child['reportedValue'];
                }
            }
        }

        return $data;
    }
}
