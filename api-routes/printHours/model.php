<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_PrintHours
{
    public function getData(string $personalNo, ?string $from, ?string $to, ?string $ktrAanr): array
    {
        $curl = new PrintoutCurl();
        $data = [];

        $params = [
            'resnr' => $personalNo,
            'includeExpenseAllowanceInHoursEntry' => 1,
            'type' => 'approved'
        ];

        if ($from) {
            $params['date'] = $from;
        }
        if ($to) {
            $params['to_date'] = $to;
        }

        if ($ktrAanr) {
            $parts = explode('-', $ktrAanr);
            $params['ktr'] = $parts[0];
            $params['aanr'] = $parts[1];
        }

        $base = PrintoutHelper::getApiBaseUrl();
        $hoursCall = $curl->_simple_call('get', "$base/v1/hours/all",
            $params, PrintoutHelper::getHeadersForApiCalls());
        $hours = json_decode($hoursCall, true) ?? [];

        /** @noinspection PhpUnhandledExceptionInspection */
        $hours = PrintoutHelper::adjustHoursByOffsetHeader($hours);

        usort($hours, function ($x, $y) {
            if ($x['date'] === $y['date']) {
                return strcmp($x['name'], $y['name']);
            }
            return ($x['date'] > $y['date']) ? 1 : -1;
        });

        $groupedHours = [];
        $allPnrs = explode(',', $personalNo);

        foreach ($allPnrs as $pnr) {
            $groupedHours[$pnr] = [];
        }
        foreach ($hours as $hour) {
            $pnr = $hour['pnr'];
            if (isset($groupedHours[$pnr])) {
                $temp[$pnr][] = $hour;
                $groupedHours[$pnr]['hours_data'] = $temp[$pnr];
            }
        }

        $data['hours'] = $groupedHours;

        $employees = [];
        foreach ($allPnrs as $pnr) {
            $data['hours'][$pnr] += $this->getGroupedStatisticData($data['hours'][$pnr]['hours_data'] ?? []);
            $employees[$pnr] = PrintoutHelper::downloadEmployee(intval($pnr), $curl);
        }
        $data['employees'] = $employees;

        return $data;
    }

    private function getGroupedStatisticData($hours_data): array
    {
        // Generate data for the grouped statistics
        $date_flag = "";
        $ktr_flag = "";

        $annual_hours = $monthly_hours = $ktr_hours = array();
        $annual_days = $monthly_days = $ktr_days = array();
        $annual_bookings = $monthly_bookings = $ktr_bookings = array();

        $annualBonusAmount = $monthlyBonusAmount = $ktrBonusAmount = array();
        $annualBonusBookings = $monthlyBonusBookings = $ktrBonusBookings = array();

        $totalBonusAmount = array();

        $stats_data = [];
        foreach ($hours_data as $row) {
            list($y, $m,) = explode('-', $row['date']);

            if ((isset($row['wageTypeExternal']) && $row['wageTypeExternal'] == 'B' && $row['origin'] !=
                    'TimeTrackEvent') || strpos($row['ltext'] ?? '', "via Expense Allowance @")) {

                if (!isset($annualBonusAmount[$y][$row['wageTypeUnit']])) {
                    $annualBonusAmount[$y][$row['wageTypeUnit']] = 0;
                }
                if (!isset($monthlyBonusAmount[$y][$m][$row['wageTypeUnit']])) {
                    $monthlyBonusAmount[$y][$m][$row['wageTypeUnit']] = 0;
                }
                if (!isset($ktrBonusAmount[$row['ktr']])) {
                    $ktrBonusAmount[$row['ktr']] = 0;
                }
                if (!isset($annualBonusBookings[$y])) {
                    $annualBonusBookings[$y] = 0;
                }
                if (!isset($monthlyBonusBookings[$y][$m])) {
                    $monthlyBonusBookings[$y][$m] = 0;
                }
                if (!isset($ktrBonusBookings[$row['ktr']])) {
                    $ktrBonusBookings[$row['ktr']] = 0;
                }
                if (!isset($totalBonusAmount[$row['wageTypeUnit']])) {
                    $totalBonusAmount[$row['wageTypeUnit']] = 0;
                }
                $annualBonusAmount[$y][$row['wageTypeUnit']] += $row['std'];
                $monthlyBonusAmount[$y][$m][$row['wageTypeUnit']] += $row['std'];
                $ktrBonusAmount[$row['wageTypeUnit']] += $row['std'];
                $totalBonusAmount[$row['wageTypeUnit']] += $row['std'];
                $annualBonusBookings[$y] += 1;
                $monthlyBonusBookings[$y][$m] += 1;
                $ktrBonusBookings[$row['ktr']] += 1;
            } else {
                if (!isset($annual_hours[$y])) {
                    $annual_hours[$y] = 0;
                }
                if (!isset($monthly_hours[$y][$m])) {
                    $monthly_hours[$y][$m] = 0;
                }
                if (!isset($ktr_hours[$row['ktr']])) {
                    $ktr_hours[$row['ktr']] = 0;
                }
                if (!isset($annual_days[$y])) {
                    $annual_days[$y] = 0;
                }
                if (!isset($monthly_days[$y][$m])) {
                    $monthly_days[$y][$m] = 0;
                }
                if (!isset($ktr_days[$row['ktr']])) {
                    $ktr_days[$row['ktr']] = 0;
                }
                if (!isset($annual_bookings[$y])) {
                    $annual_bookings[$y] = 0;
                }
                if (!isset($monthly_bookings[$y][$m])) {
                    $monthly_bookings[$y][$m] = 0;
                }
                if (!isset($ktr_bookings[$row['ktr']])) {
                    $ktr_bookings[$row['ktr']] = 0;
                }
                $annual_hours[$y] += $row['std'];
                $monthly_hours[$y][$m] += $row['std'];
                $ktr_hours[$row['ktr']] += $row['std'];
                $annual_days[$y] += $date_flag === $row['date'] ? 0 : 1;
                $monthly_days[$y][$m] += $date_flag === $row['date'] ? 0 : 1;
                $ktr_days[$row['ktr']] += $date_flag === $row['date'] && $ktr_flag === $row['ktr'] ? 0 : 1;
                $annual_bookings[$y] += 1;
                $monthly_bookings[$y][$m] += 1;
                $ktr_bookings[$row['ktr']] += 1;
            }
            $date_flag = $row['date'];
            $ktr_flag = $row['ktr'];

            // Send that data to the view
            //Bonuses
            $stats_data['annualBonusAmount'] = $annualBonusAmount;
            $stats_data['monthlyBonusAmount'] = $monthlyBonusAmount;
            $stats_data['ktrBonusAmount'] = $ktrBonusAmount;
            $stats_data['totalBonusAmount'] = $totalBonusAmount;
            $stats_data['annualBonusBookings'] = $annualBonusBookings;
            $stats_data['monthlyBonusBookings'] = $monthlyBonusBookings;
            $stats_data['ktrBonusBookings'] = $ktrBonusBookings;

            //Regular hours
            $stats_data['annual_hours'] = $annual_hours;
            $stats_data['monthly_hours'] = $monthly_hours;
            $stats_data['ktr_hours'] = $ktr_hours;
            $stats_data['annual_days'] = $annual_days;
            $stats_data['monthly_days'] = $monthly_days;
            $stats_data['ktr_days'] = $ktr_days;
            $stats_data['annual_bookings'] = $annual_bookings;
            $stats_data['monthly_bookings'] = $monthly_bookings;
            $stats_data['ktr_bookings'] = $ktr_bookings;
        }
        return $stats_data;
    }
}
