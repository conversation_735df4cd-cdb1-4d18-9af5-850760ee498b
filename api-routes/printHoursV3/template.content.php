<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_printHoursV3())->getData($_GET['personalNo'], $_GET['from'] ?? null,
        $_GET['to'] ?? null, $_GET['ktrAanr'] ?? null, $_GET['displayTimeFormat'] ?? null);
}

function asset($path): string
{
    return PrintoutHelper::translateLocalPathToServerPath(__DIR__ . $path);
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <!--suppress DuplicatedCode -->
    <link rel="stylesheet" href="<?= asset('/style.css') ?>">
    <title>PrintHoursV3</title>
</head>
<body>
<table class="header-table">
    <tr>
        <td>
            <span class="icon-label">Arbeitszeiten</span>
            <img src="<?= asset('/img/cogs-gray.png') ?>" class="icon" alt="">
        </td>
        <td>
            <span class="icon-label">Fahrzeiten</span>
            <img src="<?= asset('/img/cargo-truck.png') ?>" class="icon" alt="">
        </td>
        <td>
            <span class="icon-label">Gesamtkosten</span>
            <img src="<?= asset('/img/dollar-bill.png') ?>" class="icon" alt="">
        </td>
        <td>
            <span class="icon-label">Pausen</span>
            <img src="<?= asset('/img/coffee.png') ?>" class="icon" alt="">
        </td>
        <td>
            <span class="icon-label">Summe</span>
            ∑
        </td>
        <td>
            <span class="icon-label">Überstunden</span>
            <img src="<?= asset('/img/overtime.png') ?>" class="icon" alt="">
        </td>
        <td>
            <span class="icon-label">Anwesenheit</span>
            <img src="<?= asset('/img/attendance-icon.svg') ?>" class="icon" alt="">
        </td>
    </tr>
</table>

<?php
$counter = 0;
foreach ($data as $item) { ?>
    <?php if ($counter != 0) { ?>
        <div style="page-break-before: always"></div>
    <?php } ?>
    <p class="employee"<?= $counter >= 1 ? ' style="margin-top: 30px;"' : '' ?>>
        <?= "{$item['employeeNo']} - {$item['employeeDisplayName']}" . "  " . $item['employeeBirthDate'] ?>
    </p>

    <table class="main-table">
        <?php foreach ($item['hours'] as $key => $hours) { ?>
            <tr class="header-row">
                <td>Datum</td>
                <td class="text-left">Anfahrt</td>
                <td>Arbeitszeiten</td>
                <td>Pause/n</td>
                <td>Abfahrt</td>
                <td>
                    <img src="<?= asset('/img/cogs-gray.png') ?>" class="icon" alt="">
                </td>
                <td>
                    <img src="<?= asset('/img/cargo-truck.png') ?>" class="icon" alt="">
                </td>
                <td>
                    <img src="<?= asset('/img/dollar-bill.png') ?>" class="icon" alt="">
                </td>
                <td>
                    <img src="<?= asset('/img/coffee.png') ?>" class="icon" alt="">
                </td>
                <td>Soll</td>
                <td>∑</td>
                <td>
                    <img src="<?= asset('/img/overtime.png') ?>" class="icon" alt="">
                </td>
                <td style="border-right: 1px solid black;">
                    <img src="<?= asset('/img/attendance-icon.svg') ?>" class="icon" alt="">
                </td>
            </tr>
            <tr class="month-row">
                <?php
                list($year, $month) = explode('-', $key);
                $month = PrintoutHelper::getMonth('de', intval($month));
                $lang = PrintoutHelper::getLanguage();
                $totalDays = $item['monthlyBookings'][$key];
                ?>
                <td colspan="13" style="border-right: 1px solid black;">
                    <?= "$month, $year (Gesamtarbeitstage - $totalDays )" ?>
                </td>
            </tr>
            <?php for ($i = 0; $i < count($hours); $i++) { ?>
                <?php $class = $i % 2 == 0 ? 'even-row' : 'odd-row' ?>
                <tr class="<?= $class ?>">
                    <td></td>
                    <td colspan="13" class="text-left text-gray">
                        <?= $hours[$i]['name'] ?>
                    </td>
                </tr>
                <tr class="<?= $class ?>">
                    <td><?= "{$hours[$i]['day']}, {$hours[$i]['date']}" ?></td>
                    <td><?= $hours[$i]['driveTimeTo'] ?></td>
                    <td>
                        <?= (empty($hours[$i]['start']) ? '-' : $hours[$i]['start']) . ' - ' . (empty($hours[$i]['end']) ? '-' : $hours[$i]['end']) ?>
                    </td>
                    <td><?= $hours[$i]['breaks'] ?></td>
                    <td><?= $hours[$i]['driveTimeFrom'] ?></td>
                    <td style="text-align: right"><?= PrintoutHelper::formatTimeNumber($hours[$i]['hours']) ?></td>
                    <td><?= PrintoutHelper::formatTimeNumber($hours[$i]['dailyTotalDrive']) ?></td>
                    <td>-</td>
                    <td><?= PrintoutHelper::formatTimeNumber($hours[$i]['dailyTotalPause']) ?></td>
                    <td><?= PrintoutHelper::formatTimeNumber($hours[$i]['soll']) ?></td>
                    <td><?= PrintoutHelper::formatTimeNumber($hours[$i]['dailyTotal']) ?></td>
                    <td><?= PrintoutHelper::formatTimeNumber($hours[$i]['overtime']) ?></td>
                    <td style="border-right: 1px solid black;"><?= PrintoutHelper::formatTimeNumber($hours[$i]['dailyTotalAttendance']) ?></td>
                </tr>
                <tr class="<?= $class ?>">
                    <td></td>
                    <td colspan="13" class="text-left text-gray" style="border-right: 1px solid black;">
                        <?= nl2br($hours[$i]['comment']) ?>
                    </td>
                </tr>
            <?php } ?>
        <?php } ?>
        <tr class="total-row">
            <td colspan="5">Summe</td>
            <td style="text-align: right"><?= $item['totalWork'] ?></td>
            <td style="text-align: right"><?= $item['totalDrive'] ?></td>
            <td style="text-align: right">-</td>
            <td style="text-align: right"><?= $item['totalPause'] ?></td>
            <td style="text-align: right"><?= $item['totalSoll'] ?></td>
            <td style="text-align: right"><?= $item['totalTime'] ?></td>
            <td style="text-align: right"><?= $item['totalOvertime'] ?></td>
            <td style="text-align: right; border-right: 1px solid black;"><?= $item['totalAttendance'] ?></td>
        </tr>
    </table>

    <?php if (!empty($item['statistics'])) { ?>
        <table class="statistics">
            <?php foreach ($item['statistics'] as $projectStats) { ?>
                <tr>
                    <td><?= $projectStats['ktr'] . ' ' . $projectStats['name'] ?></td>
                    <td><?= 'Arbeitsstunden: ' . $projectStats['hours'] ?></td>
                    <td><?= 'Tage: ' . count(array_unique($projectStats['dates'])) ?></td>
                </tr>
            <?php } ?>
        </table>
    <?php } ?>

    <?php if ($item['totalOvertimeDecimal'] != 0.0) { ?>
        <table class="statistics">
            <tr>
                <td style="font-size: 18px">
                    Überstundenkontostand: <span class="font-bold"><?= $item['totalOvertime'] ?></span>
                </td>
            </tr>
        </table>
    <?php }

    $counter++;
} ?>

</body>
</html>