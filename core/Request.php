<?php

require_once __DIR__ . '/OutputType.php';
require_once __DIR__ . '/OutputTypeResult.php';

class Request
{
    public static RouteConfig $config;

    /**
     * @var string contains the raw route like MediaPrintV3/2506003 or MediaPrintV2
     */
    private static string $rawRoute;

    public static function setRawRoute(string $rawRoute): void
    {
        self::$rawRoute = $rawRoute;
    }

    /**
     * @param string $routeName the route name like "MediaPrintV2" or "MediaPrintV3". Do not use / characters here.
     *   It is compared case-insensitively and correctly handles the case when Request::rawRoute has / characters.
     */
    public static function isRoute(string $routeName): bool
    {
        if (str_contains(self::$rawRoute, '/')) {
            $route = explode('/', self::$rawRoute)[0];
            return strtolower($route) === strtolower($routeName);
        }
        return strtolower(self::$rawRoute) === strtolower($routeName);
    }

    public static OutputType $outputType;

    public static array $query = [];

    public static array $input;

    /**
     * Set in index.php for easy access in api-routes/config.php
     */
    public static string $basePathApiRoutes;

    /**
     * Set in index.php for easy access in api-routes/config.php
     */
    public static array $urlParams;

    public static function setConfig(RouteConfig $config): void
    {
        self::$config = $config;
    }

    public static function setRouteAndType(OutputTypeResult $route): void
    {
        self::$rawRoute = $route->apiRoute;
        self::$outputType = $route->type;
    }

    public static function setQuery($query): void
    {
        self::$query = $query;
    }

    public static function method()
    {
        return $_SERVER['REQUEST_METHOD'];
    }

    /**
     * @return mixed the decoded JSON body as an associative array
     */
    public static function parse_request_body_as_json_or_die(): mixed
    {
        $json = file_get_contents('php://input');
        $decoded = json_decode($json, true);
        if ($decoded == null) {
            die_with_response_code(Response::BAD_REQUEST, 'Request body is not valid JSON');
        }
        return $decoded;
    }

    /**
     * This reads from php://input and caches the result.
     *
     * @param string|null $key if null, return an empty array, otherwise return the key value from decoded JSON
     * @param mixed $default the value to return if the key is not found in the decoded JSON
     * @return mixed $default or the key value from decoded JSON
     */
    public static function input(string|null $key = null, mixed $default = null): mixed
    {
        if (isset(self::$input)) {
            $data = self::$input;
        } else {
            $json = file_get_contents('php://input');
            $data = json_decode($json, true) ?? [];
            self::$input = $data;
        }

        return empty($key) ? $data : ($data[$key] ?? $default);
    }

    public static function isPdf(): bool
    {
        return self::$outputType == OutputType::Pdf;
    }

    public static function isPlaywright(): bool
    {
        return self::$config->renderEngine == RenderEngine::playwright;
    }

    public static function isWkhtmltopdf(): bool
    {
        return self::$config->renderEngine == RenderEngine::wkhtmltopdf;
    }
}