<?php
declare(strict_types=1);
require_once __DIR__ . '/api-routes.php';

$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once($configFile);
}

function setProxyOptions($curlHandle): void
{
    if (defined('PRINTOUT_PROXY')) {
        list($url, $port) = explode(':', trim(PRINTOUT_PROXY));
        curl_setopt($curlHandle, CURLOPT_PROXY, $url . ':' . $port);
        curl_setopt($curlHandle, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curlHandle, CURLOPT_SSL_VERIFYPEER, false);
    }
}

function handleCurlError($ch, $response, $statusCode): void
{
    if ($response === false) {
        $error = curl_error($ch);
        curl_close($ch);
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode([
            ' ' => 'CURL Error',
            'message' => $error,
            'status' => $statusCode
        ]);
        exit;
    }
}

// Handle login request
if (isset($_GET['login'])) {
    header('Content-Type: application/json');

    $principal = $_GET['principal'] ?? '';
    $username = $_GET['username'] ?? '';
    $password = $_GET['password'] ?? '';

    if (empty($principal) || empty($username) || empty($password)) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required parameters']);
        exit;
    }

    // First get the base URL from principals endpoint
    $ch = curl_init("https://api.baubuddy.de/index.php/v1/principals/$principal");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw',
        'Content-Type: application/json',
        'Principal: master_principal'
    ]);
    setProxyOptions($ch);

    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    handleCurlError($ch, $response, $statusCode);

    curl_close($ch);

    if ($statusCode !== 200) {
        http_response_code($statusCode);
        echo json_encode([
            'error' => 'Principal check failed',
            'message' => 'Server returned status ' . $statusCode,
            'response' => $response,
            'status' => $statusCode
        ]);
        exit;
    }

    $principalData = json_decode($response, true);

    // Determine base URL
    if (empty($principalData['api_url'])) {
        $fromEnv = getenv('VERO_API_URL');
        if (empty($fromEnv)) {
            $baseUrl = 'https://api.baubuddy.de/index.php';
        } else {
            $baseUrl = $fromEnv;
        }
    } else {
        $baseUrl = $principalData['api_url'];
    }

    // Ensure URL ends with /index.php
    if (str_ends_with($baseUrl, '/')) {
        $baseUrl = substr($baseUrl, 0, -1);
    }
    if (!str_ends_with($baseUrl, '/index.php')) {
        $baseUrl .= '/index.php';
    }

    // Now do the login
    $ch = curl_init("$baseUrl/v1/login");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'username' => $username,
        'password' => $password
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Principal: ' . $principal,
        'Authorization: Basic dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw'
    ]);
    setProxyOptions($ch);

    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    handleCurlError($ch, $response, $statusCode);

    curl_close($ch);

    if ($statusCode !== 200) {
        http_response_code($statusCode);
        echo json_encode([
            'error' => 'Login failed',
            'message' => 'Server returned status ' . $statusCode,
            'response' => $response,
            'status' => $statusCode,
            'details' => json_decode($response, true) ?? $response
        ]);
        exit;
    }

    $loginData = json_decode($response, true);
    if (empty($loginData['oauth']['access_token'])) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Invalid response format',
            'message' => 'No token found in response',
            'response' => $response,
            'parsed' => $loginData
        ]);
        exit;
    }

    echo json_encode(['token' => $loginData['oauth']['access_token']]);
    exit;

} else if (isset($_GET['swagger_try_it_out'])) {

    $route = $_GET['route'] ?? '';
    $method = strtoupper($_GET['method'] ?? 'GET');
    $params = $_GET['params'] ?? [];

    $url = "/index.php/$route";
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    $urlParams = array_change_key_case($_GET);
    $headers = [
        'Principal: ' . ($urlParams['headers']['principal'] ?? 'default_test'),
        'Authorization: ' . ($urlParams['headers']['authorization'] ?? '_')
    ];

    $insideDockerEnv = getenv('INSIDE_DOCKER_ENV');

    if ($insideDockerEnv) {
        $host = gethostbyname('host.docker.internal');
        if ($host === 'host.docker.internal') {
            // If that fails, use the default gateway IP
            $gateway = exec("ip route | awk '/default/ { print $3 }'");
            $host = $gateway ?: 'localhost';
        }
    } else {
        $host = 'localhost';
    }

    if (isset($_GET['port']) && !$insideDockerEnv) {
        /** @noinspection HttpUrlsUsage */
        $url = "http://$host:" . $_GET['port'] . $url;
    } else {
        /** @noinspection HttpUrlsUsage */
        $url = "http://$host" . $url;
    }
    error_log($url);

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    setProxyOptions($ch);

    if ($method === 'POST' && isset($_GET['body'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, $_GET['body']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($headers, ['Content-Type: application/json']));
    }

    $response = curl_exec($ch);
    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if ($response === false || $statusCode >= 400) {
        $error = curl_error($ch);
        curl_close($ch);
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'CURL Error',
            'message' => $error . ' : ' . $response . ' (Status: ' . $statusCode . ')',
        ]);
        exit;
    }

    $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);

    // Forward the status code, content type and response
    http_response_code($statusCode);
    header('Content-Type: ' . $contentType);
    echo $response;
    exit;
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta name="description" content="API Printouts"/>
    <title>Printouts Route Explorer</title>
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@5.11.0/swagger-ui.css"/>
    <!--suppress CssUnusedSymbol -->
    <style>
        html, body {
            margin: 0;
            padding: 0;
        }

        /* patch away the purple and set nicer paddings */
        .swagger-ui .markdown code, .swagger-ui .renderedMarkdown code {
            color: unset;
            padding: 0 3px;
        }

        /* set larger vertical paddings */
        .swagger-ui .markdown pre>code, .swagger-ui .renderedMarkdown pre>code {
            padding: 10px;
        }

        .login-container {
            font-family: sans-serif;
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #d9d9d9;
        }

        .login-container .input-group {
            display: inline-block;
            margin-right: 15px;
        }

        .login-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #3b4151;
        }

        .login-container input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }

        #loginButton {
            padding: 8px 20px;
            background: rgb(55, 113, 223);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            vertical-align: bottom;
            margin-bottom: 2px;
            position: relative;
        }

        .login-container button:disabled {
            background: rgb(147, 174, 224);
            cursor: not-allowed;
        }

        .login-container button .spinner {
            display: none;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }

        .login-container button.loading .spinner {
            display: block;
        }

        .login-container button.loading .text {
            visibility: hidden;
        }

        .token-group {
            display: flex;
            flex-direction: column;
            width: 100%;
            margin-top: 20px;
            position: relative;
        }

        .token-group .clear-button {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 18px;
            padding: 4px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .token-group .clear-button:hover {
            color: #666;
        }

        #token {
            width: auto;
            padding-right: 25px;
            background: #eee;
        }

        .swagger-ui .info {
            margin: 0;
        }
    </style>
</head>
<body>
<div class="login-container">
    <div class="input-group">
        <label for="principal">Principal</label>
        <input type="text" id="principal" placeholder="Enter principal">
    </div>
    <div class="input-group">
        <label for="username">Username</label>
        <input type="text" id="username" placeholder="Enter username">
    </div>
    <div class="input-group">
        <label for="password">Password</label>
        <input type="password" id="password" placeholder="Enter password">
    </div>
    <button onclick="login()" id="loginButton">
        <span class="spinner"></span>
        <span class="text">Login</span>
    </button>
    <div class="token-group">
        <!--suppress HtmlFormInputWithoutLabel -->
        <input type="text" id="token" readonly placeholder="Auth Token">
        <button type="button" class="clear-button" onclick="clearToken()" title="Clear token">×</button>
    </div>
</div>
<div id="swagger-ui"></div>
<script src="https://unpkg.com/swagger-ui-dist@5.11.0/swagger-ui-bundle.js" crossorigin></script>
<!--suppress JSUnresolvedReference -->
<script>
    // Load saved values
    document.getElementById('principal').value = localStorage.getItem('printouts-explorer-principal') || 'default_test';
    document.getElementById('username').value = localStorage.getItem('printouts-explorer-username') || '';
    document.getElementById('password').value = localStorage.getItem('printouts-explorer-password') || '';
    document.getElementById('token').value = localStorage.getItem('printouts-explorer-token') || '';

    ['principal', 'username', 'password'].forEach(id => {
        document.getElementById(id).addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                login();
            }
        });
    });

    function handleLoginError(error) {
        console.error('Login error:', error);
        alert(error.message || 'An error occurred during login');
    }

    async function login() {
        const loginButton = document.getElementById('loginButton');
        loginButton.classList.add('loading');
        loginButton.disabled = true;

        const principal = document.getElementById('principal').value;
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!principal || !username || !password) {
            handleLoginError(new Error('To login, fill out principal, username and password'));
            loginButton.classList.remove('loading');
            loginButton.disabled = false;
            return;
        }

        // Save inputs
        localStorage.setItem('printouts-explorer-principal', principal);
        localStorage.setItem('printouts-explorer-username', username);
        localStorage.setItem('printouts-explorer-password', password);

        const params = new URLSearchParams({
            login: '1',
            principal: principal,
            username: username,
            password: password
        });

        const response = await fetch(`${window.location.pathname}?${params.toString()}`);
        const data = await response.json();

        if (!response.ok) {
            handleLoginError(new Error(
                data.message
                    ? `${data.error}: ${data.message}\nServer response: ${data.response}`
                    : `Login failed: ${response.status}`
            ));
            loginButton.classList.remove('loading');
            loginButton.disabled = false;
            return;
        }

        if (!data.token) {
            handleLoginError(new Error('No token received from server'));
            loginButton.classList.remove('loading');
            loginButton.disabled = false;
            return;
        }

        // Save and display token
        localStorage.setItem('printouts-explorer-token', data.token);
        document.getElementById('token').value = data.token;

        loginButton.classList.remove('loading');
        loginButton.disabled = false;
    }

    function clearToken() {
        document.getElementById('token').value = '';
        localStorage.removeItem('printouts-explorer-token');
    }
</script>
<!--suppress JSCheckFunctionSignatures, JSUnresolvedReference -->
<script>
    <?php
    $paths = [];

    $dirMapping = getRouteDirectoryMapping();
    $routes = array_keys($dirMapping);

    $routePrefix = '/index.php/';
    $basePathApiRoutes = __DIR__ . '/api-routes/';

    foreach ($routes as $route) {
        $parameters = [];
        $requestBody = null;

        $routeConfigPath = $basePathApiRoutes . $route . '/config.php';
        $config = new RouteConfig();
        if (!file_exists($routeConfigPath)) {
            continue;
        }

        /**
         * @var RouteConfig $config
         */
        $config = require_once $routeConfigPath;

        $operation = strtolower($config->requestMethod->name);
        $endpoint = $routePrefix . $route;

        $segments = $config->getRouteSegments();
        if (!empty($segments)) {
            foreach ($segments as $segment) {
                $endpoint .= "/{{$segment->name}}";
                $parameters[] = [
                    'in' => 'path',
                    'name' => $segment->name,
                    'required' => $segment->required,
                    'schema' => ['type' => $segment->type->getName()],
                    'example' => $segment->type->getSwaggerExample(),
                ];
            }
        }

        /** @var RouteConfigParameter $parameter */
        foreach ($config->getParameters() as $parameter) {
            if ($parameter->name === 'imagesPerPage') {
                $parameters[] = [
                    '$ref' => '#/components/parameters/imagesPerPageParam'
                ];
                continue;
            }

            $schema = [];
            $schemaType = $parameter->type->getName();
            if ($schemaType !== null) {
                $schema = ['type' => $schemaType];
            }
            $parameters[] = [
                'in' => 'query',
                'name' => $parameter->name,
                'required' => $parameter->required,
                'schema' => $schema,
                'example' => $parameter->type->getSwaggerExample(),
            ];
        }
        if ($route === 'MediaPrintV3') {
            $requestBody = ['$ref' => '#/components/requestBodies/MediaPrintV3Body'];
        } elseif (in_array($route, ['MediaProjectV2', 'MediaWorkingOrderV2'])) {
            $requestBody = ['$ref' => '#/components/requestBodies/MediaPrintV2Body'];
        } elseif (in_array($route, ['MediaProject', 'MediaWorkingOrder'])) {
            $requestBody = ['$ref' => '#/components/requestBodies/MediaPrintBody'];
        }
        $operationDesc = "";
        if ($route === 'MediaPrintV3') {
            $operationDesc = <<<DESC
                Accepts an array of file documentation objects. Each object must include `rel_key1` (the file ID) and optionally `reportedValues`, which is an array of `{id, value}` pairs. The IDs in `reportedValues` must exactly match the IDs of the schema. The route expects a stable schema where the title and the values below start on index 2. Index 0 and 1 are for the headline and the photo, and are ignored.
                
                To retrieve the schema, call `GET v1/documentation/documentation_schema/{schemaId}`
                
                **Example:**
                The "Image Properties for MediaPrint" schema contains these positions:
                ```json
                [
                  { "id": 3, "title": "Image Properties", "type": "string" },
                  { "id": 4, "title": "Preview", "type": "photo" },
                  { "id": 5, "title": "Title", "type": "string" },
                  { "id": 6, "title": "Show Order", "type": "checkbox" },
                  { "id": 7, "title": "Show Uploader", "type": "checkbox" },
                  { "id": 8, "title": "Show Comments", "type": "checkbox" },
                  { "id": 9, "title": "Show Creation Date", "type": "checkbox" },
                  { "id": 10, "title": "Show Upload Date", "type": "checkbox" }
                ]
                ```
                Then a valid request body could be:
                ```json
                [
                  {
                    "rel_key1": 123456,
                    "reportedValues": [
                      { "id": 5, "value": "My file title" },
                      { "id": 6, "value": true },
                      { "id": 7, "value": false },
                      { "id": 8, "value": true },
                      { "id": 9, "value": false },
                      { "id": 10, "value": true }
                    ]
                  }
                ]
                ```
             DESC;
        }

        $paths[$endpoint] = [
            $operation => array_merge(['description' => $operationDesc,], compact('parameters', 'requestBody'))
        ];
    }

    ksort($paths);

    $components = [
        'schemas' => [
            'File' => [
                'type' => 'object',
                'required' => ['fileId'],
                'properties' => [
                    'fileId' => ['type' => 'integer', 'example' => 175032],
                    'description' => ['type' => 'string', 'example' => 'test'],
                    'showWorkingOrderNumber' => ['type' => "boolean", 'example' => true],
                    'showUploader' => ['type' => 'boolean', 'example' => true],
                    'showComments' => ['type' => 'boolean', 'example' => true],
                    'showUploadDate' => ['type' => 'boolean', 'example' => true],
                    'showCreationDate' => ['type' => 'boolean', 'example' => true],
                ]
            ],
            'MediaPrintV3File' => [
                'type' => 'object',
                'required' => ['rel_key1', 'reportedValues'],
                'properties' => [
                    'rel_key1' => [
                        'type' => 'integer',
                        'example' => 207418,
                        'description' => 'The file ID to print.'
                    ],
                    'reportedValues' => [
                        'type' => 'array',
                        'description' => 'Array of objects with id (schema field ID) and value (string or boolean). The meaning and type of each id is defined in the schema referenced by schemaId. You must fetch the schema from `/index.php/v1/documentation/documentation_schema/{schemaId}.json` to know what each id means and what type of value is expected.',
                        'items' => [
                            '$ref' => '#/components/schemas/ReportedValue'
                        ],
                        'example' => [
                            ['id' => 23802, 'value' => 'first file'],
                            ['id' => 23803, 'value' => true],
                            ['id' => 23804, 'value' => true],
                            ['id' => 23805, 'value' => false],
                            ['id' => 23806, 'value' => true],
                            ['id' => 23807, 'value' => false]
                        ]
                    ]
                ],
                'description' => 'Each object represents a file to print. The reportedValues array must use IDs from the schema referenced by schemaId. Each ID corresponds to a specific field (e.g., description, showWorkingOrderNumber, etc.) as defined in the schema. The value type is determined by the schema: string for description, boolean for visibility fields. You must inspect the schema to know which ID means what.'
            ],
            'ReportedValue' => [
                'type' => 'object',
                'required' => ['id', 'value'],
                'properties' => [
                    'id' => [
                        'type' => 'integer',
                        'description' => 'Field ID from the schema. The meaning of this ID (e.g., description, showWorkingOrderNumber, etc.) is defined in the schema referenced by schemaId.'
                    ],
                    'value' => [
                        'description' => 'Value for the field. The type is defined by the schema: string for description, boolean for visibility fields.',
                        'oneOf' => [
                            ['type' => 'string'],
                            ['type' => 'boolean']
                        ]
                    ]
                ],
                'example' => ['id' => 38433, 'value' => 'first file']
            ],
        ],
        'parameters' => [
            'imagesPerPageParam' => [
                'name' => 'imagesPerPage',
                'in' => 'query',
                'required' => true,
                'example' => 1,
                'schema' => [
                    'type' => 'integer',
                    'enum' => [1, 2, 3, 4],
                    'minimum' => 1,
                    'maximum' => 4
                ]
            ]
        ],
        'requestBodies' => [
            'MediaPrintBody' => [
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'imagesPerPage' => [
                                    'type' => 'integer',
                                    'minimum' => 1,
                                    'maximum' => 4,
                                    'example' => 1,
                                ],
                                'saveHardCopy' => [
                                    'type' => 'boolean',
                                    'example' => true
                                ],
                                'images' => [
                                    'type' => 'array',
                                    'minItems' => 1,
                                    'maxItems' => 100,
                                    'items' => [
                                        '$ref' => '#/components/schemas/File'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'MediaPrintV2Body' => [
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'saveHardCopy' => [
                                    'type' => 'boolean',
                                    'example' => true
                                ],
                                'images' => [
                                    'type' => 'array',
                                    'items' => [
                                        '$ref' => '#/components/schemas/File'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'MediaPrintV3Body' => [
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'description' => 'Each object represents a file to print. The reportedValues array must use IDs from the schema referenced by schemaId. Each ID corresponds to a specific field (e.g., description, showWorkingOrderNumber, etc.) as defined in the schema. The value type is determined by the schema: string for description, boolean for visibility fields. You must inspect the schema to know which ID means what.\n\nTo retrieve the schema, call: `/index.php/v1/documentation/documentation_schema/{schemaId}.json`.',
                            'properties' => [
                                'rel_key1' => [
                                    'type' => 'integer',
                                    'example' => 207664,
                                    'description' => 'The file ID to print.'
                                ],
                                'reportedValues' => [
                                    'type' => 'array',
                                    'description' => 'Array of objects with id (schema field ID) and value (string or boolean). The meaning of each id is defined in the schema referenced by schemaId. For example, one id may mean description (string), others may mean show/hide flags (boolean).',
                                    'items' => [
                                        '$ref' => '#/components/schemas/ReportedValue'
                                    ],
                                    'example' => [
                                        ['id' => 17372, 'value' => 'first file'],
                                        ['id' => 17373, 'value' => true],
                                        ['id' => 17374, 'value' => true],
                                        ['id' => 17375, 'value' => false],
                                        ['id' => 17376, 'value' => true],
                                        ['id' => 17377, 'value' => false]
                                    ]
                                ]
                            ],
                            'example' => [
                                [
                                    'rel_key1' => 207664,
                                    'reportedValues' => [
                                        ['id' => 17372, 'value' => 'first file'],
                                        ['id' => 17373, 'value' => true],
                                        ['id' => 17374, 'value' => true],
                                        ['id' => 17375, 'value' => false],
                                        ['id' => 17376, 'value' => true],
                                        ['id' => 17377, 'value' => false]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
        ]
    ];
    $gitCommit = exec("git rev-parse --short HEAD 2>&1", $gitCommitOutput, $gitCommitReturn);
    if ($gitCommitReturn === 0) {
        $commit = $gitCommit;
        $gitCommit = "Git commit: " . $gitCommit;
        $date = exec("git show --no-patch --format=%ci $commit 2>&1", $gitDateOutput, $gitDateReturn);
        // ignore date errors
        if ($gitDateReturn === 0) {
            $gitCommit .= "    @    " . $gitDateOutput[0];
        }
    } else {
        $gitCommit = "Git commit: Failed to get commit: " . str_replace("\t", "", implode("<br>", $gitCommitOutput));
    }
    $spec = [
        'openapi' => '3.0.0',
        'info' => [
            'title' => 'Routes',
            'description' => "You can append **.html** or **.json** to each route to get the JSON or the HTML content. Example: **/index.php/Arbeitszeiterfassung.json**<br>The default response is a PDF file.<br><br>You can set the <b>appendPdfsToPrintout=true</b> query parameter to any route, which will append all PDF files inside type=photo document positions to the final printout PDF. Note that it will only work for printouts which downloads documents.<br><br>All routes are case-insensitive.<br><br>$gitCommit"
        ],
        'paths' => $paths,
        'components' => $components
    ];
    ?>

    const spec = <?= json_encode($spec, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) ?>;

    window.onload = () => {
        window.ui = SwaggerUIBundle({
            spec: spec,
            dom_id: '#swagger-ui',
            tryItOutEnabled: true,
            requestInterceptor: (req) => {
                // Modify the request to go through our proxy endpoint
                const originalUrl = new URL(req.url);
                const route = originalUrl.pathname.replace('/index.php/', '');

                const proxyUrl = new URL(window.location.href);
                proxyUrl.searchParams.set('swagger_try_it_out', '1');
                proxyUrl.searchParams.set('route', route);
                proxyUrl.searchParams.set('method', req.method);

                // Add query parameters
                const searchParams = new URLSearchParams(originalUrl.search);
                for (const [key, value] of searchParams.entries()) {
                    proxyUrl.searchParams.append('params[' + key + ']', value);
                }

                // Add default headers if not present
                if (!req.headers) {
                    req.headers = {};
                }
                if (!req.headers['principal']) {
                    req.headers['principal'] = document.getElementById('principal').value || 'default_test';
                }
                if (!req.headers['authorization']) {
                    const token = document.getElementById('token').value;
                    req.headers['authorization'] = token ? `Bearer ${token}` : '_';
                }

                for (const [key, value] of Object.entries(req.headers)) {
                    proxyUrl.searchParams.append('headers[' + key + ']', value);
                }

                if (req.body) {
                    proxyUrl.searchParams.set('body', req.body);
                }
                if (window.location.port) {
                    proxyUrl.searchParams.set('port', window.location.port);
                }

                req.url = proxyUrl.toString();
                return req;
            },
            responseInterceptor: (response) => {
                if (response.status >= 400) {
                    let errorDetails = `Status: ${response.status}\n`;

                    try {
                        if (typeof response.data === 'object') {
                            errorDetails += `Error: ${response.data.error || 'Unknown error'}\n`;
                            errorDetails += `Message: ${response.data.message || 'No details provided'}\n`;

                            if (response.data.details) {
                                errorDetails += `Details: ${JSON.stringify(response.data.details, null, 2)}\n`;
                            }
                        } else if (typeof response.data === 'string') {
                            errorDetails += `Response: ${response.data}\n`;
                        }
                    } catch (e) {
                        errorDetails += `Failed to parse error details: ${e.message}`;
                    }

                    alert(errorDetails);
                    return response;
                }

                // Handle file downloads
                const contentType = response.headers['content-type'];
                if (contentType && (
                    contentType.includes('application/pdf') ||
                    contentType.includes('application/json') ||
                    contentType.includes('text/html')
                )) {
                    const blob = new Blob([response.data], {type: contentType});
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'output.' + (contentType.includes('pdf') ? 'pdf' :
                        contentType.includes('json') ? 'json' : 'html');
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                }
                return response;
            }
        });
    };
</script>
</body>
</html>
