<?php
require_once __DIR__ . '/SchemaHelper.php';

class SampleDocumentationCreator
{
    private const CACHE_FILE = __DIR__ . '/created_documents_cache.json';
    private string $baseDir;
    private bool $forceDocumentationCreation;
    /**
     * Store as a variable since it caches the access tokens to later make the schema invisible.
     */
    private ?SchemaHelper $schemaHelper = null;

    public function __construct(string $baseDir, bool $forceDocumentationCreation = false)
    {
        $this->baseDir = $baseDir;
        $this->forceDocumentationCreation = $forceDocumentationCreation;
    }

    private function loadCache(): array
    {
        if (!file_exists(self::CACHE_FILE)) {
            return [];
        }
        return json_decode(file_get_contents(self::CACHE_FILE), true) ?? [];
    }

    private function saveCache(array $cache): void
    {
        file_put_contents(self::CACHE_FILE, json_encode($cache, JSON_PRETTY_PRINT));
    }

    private function getCacheKey(string $schemaPath): string
    {
        return md5($schemaPath);
    }

    private function shouldCreateNewDocument(array $cacheEntry, string $schemaPath): bool
    {
        if ($this->forceDocumentationCreation) {
            return true;
        }

        if (!isset($cacheEntry['createdAt']) || !isset($cacheEntry['schemaHash'])) {
            return true;
        }

        // Check if it's a new day
        $lastCreatedDate = date('Y-m-d', strtotime($cacheEntry['createdAt']));
        $today = date('Y-m-d');
        if ($lastCreatedDate !== $today) {
            return true;
        }

        // Check if schema has changed
        $currentSchemaHash = md5_file($schemaPath);
        return $currentSchemaHash !== $cacheEntry['schemaHash'];
    }

    /**
     * @param array $schemaFiles
     * @param RelType|null $relType if null, -project from $options is used
     * @return array with schema_id and documentId keys
     */
    public function createDocumentations(array $schemaFiles, RelType|null $relType = null): array
    {
        $cache = $this->loadCache();
        $results = [];

        foreach ($schemaFiles as $schemaFile) {
            $fullPath = $this->baseDir . '/' . $schemaFile;
            if (!file_exists($fullPath)) {
                throw new RuntimeException("Schema file not found: $fullPath");
            }

            $cacheKey = $this->getCacheKey($fullPath);
            $cacheEntry = $cache[$cacheKey] ?? [];

            if (!$this->shouldCreateNewDocument($cacheEntry, $fullPath)) {
                $results[] = [
                    'schemaId' => $cacheEntry['schemaId'],
                    'documentId' => $cacheEntry['documentId']
                ];
                continue;
            }

            if ($this->schemaHelper === null) {
                $this->schemaHelper = new SchemaHelper();
            }
            $result = $this->schemaHelper->createSampleDocumentForSchema(["schema" => $fullPath], $relType);

            $cache[$cacheKey] = [
                'createdAt' => date('Y-m-d H:i:s'),
                'schemaHash' => md5_file($fullPath),
                'schemaId' => $result['schemaId'],
                'documentId' => $result['documentId']
            ];

            $results[] = [
                'schemaId' => $result['schemaId'],
                'documentId' => $result['documentId']
            ];
        }

        $this->saveCache($cache);
        return $results;
    }

    /**
     * Otherwise the frontends get overloaded with the amount of created schemas.
     */
    public function deleteSchemasByIds(array $ids): void
    {
        if ($this->schemaHelper === null) {
            $this->schemaHelper = new SchemaHelper();
        }
        $this->schemaHelper->deleteSchemasByIds($ids);
    }
}