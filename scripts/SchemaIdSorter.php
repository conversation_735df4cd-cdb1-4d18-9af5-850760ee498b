<?php

class SchemaIdSorter
{
    /**
     * Exit the script when any duplicated id is found.
     */
    private static function validateAllIdsUnique(array $json): void
    {
        $idMap = [];
        foreach ($json["positions"] as $position) {
            if (array_key_exists($position["id"], $idMap)) {
                echo "This ID is duplicated in the schema: " . $position["id"];
                exit(1);
            }
            $idMap[$position["id"]] = $position["id"];
        }
    }

    public static function overrideSchemaJsonWithSortedIds(): void
    {
        global $argv;

        if (count($argv) === 1) {
            echo "You need to pass the schema.json path as an argument!";
            exit(1);
        }

        $path = $argv[1];

        if (!file_exists($path)) {
            echo "The passed path: $path does not exist!";
            exit(1);
        }

        $json = file_get_contents($path);
        $jsonArray = json_decode($json, true);

        if ($jsonArray === null) {
            echo "The file: " . $path . " could not be decoded as JSON!";
            exit(1);
        }

        // store the mapping of old id to new id
        $idMap = [];

        if (!array_key_exists('positions', $jsonArray)) {
            echo "The passed JSON file: " . $path . " does not have the positions key!";
            exit(1);
        }

        self::validateAllIdsUnique($jsonArray);

        // start with ID 1
        $newId = 1;
        foreach ($jsonArray["positions"] as &$position) {
            $oldId = $position["id"];

            $position["id"] = $newId;
            $idMap[$oldId] = $newId;

            $newId++;
        }

        foreach ($jsonArray["positions"] as &$position) {
            if (isset($position["parentId"])) {
                $oldParentId = $position["parentId"];

                if (!isset($idMap[$oldParentId])) {
                    echo "Error: oldParentId: $oldParentId is not in " . print_r($idMap, true);
                    exit(1);
                }

                $position["parentId"] = $idMap[$oldParentId];
            }

            if (isset($position["displayInside"])) {
                $oldDisplayInside = $position["displayInside"];

                if (!isset($idMap[$oldDisplayInside])) {
                    echo "Error: oldDisplayInside: $oldDisplayInside is not in " . print_r($idMap, true);
                    exit(1);
                }

                $position["displayInside"] = $idMap[$oldDisplayInside];
            }

            if (isset($position["prevSiblingId"]) && $position["prevSiblingId"] != null) {
                $oldPreviousSiblingId = $position["prevSiblingId"];

                if (!isset($idMap[$oldPreviousSiblingId])) {
                    echo "Error: oldPreviousSiblingId: $oldPreviousSiblingId is not in " . print_r($idMap, true);
                    exit(1);
                }

                $position["prevSiblingId"] = $idMap[$oldPreviousSiblingId];
            }

            if (isset($position["nextSiblingId"]) && $position["nextSiblingId"] != null) {
                $oldNextSiblingId = $position["nextSiblingId"];

                if (!isset($idMap[$oldNextSiblingId])) {
                    echo "Error: oldNextSiblingId: $oldNextSiblingId is not in " . print_r($idMap, true);
                    exit(1);
                }

                $position["nextSiblingId"] = $idMap[$oldNextSiblingId];
            }
        }

        $newJson = json_encode($jsonArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        if ($newJson === false) {
            echo "Unable to convert \$jsonArray back into JSON string!";
            exit(1);
        }

        $written = file_put_contents($path, $newJson);
        if ($written === false) {
            echo "Failed to write to $path";
            exit(1);
        }

        echo "Successfully overridden $path with sorted IDs";
    }
}

// Pass just one file argument. The file will be overridden with IDs starting at 1.
// parentIds are also adjusted accordingly to match with the new IDs.

// php SchemaIdSorter.php /path/to/schema.json
SchemaIdSorter::overrideSchemaJsonWithSortedIds();